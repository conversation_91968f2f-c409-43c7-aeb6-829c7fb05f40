import 'package:flutter/foundation.dart';
import '../../data/models/user_model.dart';
import '../../core/services/backend_service.dart';
import '../../core/constants/app_constants.dart';
// import '../../core/errors/failures.dart';

class AuthProvider extends ChangeNotifier {
  final BackendService _backend = BackendService.instance;

  UserModel? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _currentUser != null;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() async {
    try {
      // Verificar si hay una sesión activa
      final isLoggedIn = await _backend.isLoggedIn();
      if (isLoggedIn) {
        final user = await _backend.getCurrentUser();
        if (user != null) {
          // En modo mock, crear un usuario de prueba
          _currentUser = UserModel(
            id: 'mock_user_id',
            email: '<EMAIL>',
            name: '<PERSON>uario de Prueba',
            role: 'Administrador',
            isActive: true,
            createdAt: DateTime.now(),
          );
          notifyListeners();
        }
      } else {
        // Usuario no autenticado
        _currentUser = null;
        notifyListeners();
      }
    } catch (e) {
      // Usuario no autenticado
      _currentUser = null;
      notifyListeners();
    }
  }

  Future<bool> signIn(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // Validación básica para modo mock
      if (email.isNotEmpty && password.length >= 6) {
        // Simular login exitoso
        _currentUser = UserModel(
          id: 'user_${DateTime.now().millisecondsSinceEpoch}',
          email: email,
          name: email.split('@')[0].toUpperCase(),
          role: 'Administrador',
          isActive: true,
          createdAt: DateTime.now(),
        );
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Email o contraseña inválidos');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Error al iniciar sesión: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> signUp(
    String email,
    String password,
    String name,
    String role,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      // Validación básica para modo mock
      if (email.isNotEmpty && password.length >= 6 && name.isNotEmpty) {
        // Simular registro exitoso
        _currentUser = UserModel(
          id: 'user_${DateTime.now().millisecondsSinceEpoch}',
          email: email,
          name: name,
          role: role,
          isActive: true,
          createdAt: DateTime.now(),
        );
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Datos inválidos para el registro');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Error al crear cuenta: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> signOut() async {
    _setLoading(true);

    try {
      // Simular logout exitoso
      _currentUser = null;
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Error al cerrar sesión');
      _setLoading(false);
    }
  }

  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      await _backend.account.createRecovery(
        email: email,
        url: 'https://your-app.com/reset-password', // Cambiar por tu URL
      );
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('Error al enviar correo de recuperación: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateProfile(UserModel updatedUser) async {
    _setLoading(true);
    _clearError();

    try {
      await _backend.databases.updateDocument(
        databaseId: AppConstants.appwriteDatabaseId,
        collectionId: AppConstants.usersCollectionId,
        documentId: updatedUser.id,
        data: updatedUser.toJson(),
      );
      _currentUser = updatedUser;
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al actualizar perfil');
      _setLoading(false);
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
