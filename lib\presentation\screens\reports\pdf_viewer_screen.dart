import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/app_utils.dart';

class PDFViewerScreen extends StatefulWidget {
  final String pdfPath;
  final String title;

  const PDFViewerScreen({
    super.key,
    required this.pdfPath,
    this.title = 'Visualizar PDF',
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen> {
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPDF();
  }

  Future<void> _loadPDF() async {
    try {
      // Verificar que el archivo existe
      final file = File(widget.pdfPath);
      if (!await file.exists()) {
        throw Exception('El archivo PDF no existe');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _sharePDF() async {
    try {
      if (kIsWeb) {
        AppUtils.showSnackBar(context, 'Compartir no disponible en web');
        return;
      }

      await Share.shareXFiles(
        [XFile(widget.pdfPath)],
        text: 'Reporte MTTO 60: ${widget.title}',
        subject: widget.title,
      );

      if (mounted) {
        AppUtils.showSnackBar(context, 'PDF compartido exitosamente');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'Error al compartir PDF: $e');
      }
    }
  }

  Future<void> _printPDF() async {
    try {
      final file = File(widget.pdfPath);
      final bytes = await file.readAsBytes();

      await Printing.layoutPdf(
        onLayout: (format) async => bytes,
        name: widget.title,
      );
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'Error al imprimir PDF: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (!_isLoading && _error == null) ...[
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _sharePDF,
              tooltip: 'Compartir PDF',
            ),
            IconButton(
              icon: const Icon(Icons.print),
              onPressed: _printPDF,
              tooltip: 'Imprimir PDF',
            ),
          ],
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Cargando PDF...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: AppColors.error),
            const SizedBox(height: 16),
            Text(
              'Error al cargar PDF',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: AppColors.textSecondary),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: const Text(
                'Volver',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    }

    // Mostrar el PDF usando PdfPreview
    return FutureBuilder<Uint8List>(
      future: _loadPDFBytes(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        if (!snapshot.hasData) {
          return const Center(child: Text('No se pudo cargar el PDF'));
        }

        return PdfPreview(
          build: (format) => snapshot.data!,
          allowPrinting: true,
          allowSharing: !kIsWeb,
          canChangePageFormat: false,
          canDebug: false,
          maxPageWidth: 700,
          pdfFileName: widget.title,
        );
      },
    );
  }

  Future<Uint8List> _loadPDFBytes() async {
    final file = File(widget.pdfPath);
    return await file.readAsBytes();
  }
}
