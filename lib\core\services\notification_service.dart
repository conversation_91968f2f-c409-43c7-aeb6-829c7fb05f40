import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();
  
  static bool _initialized = false;
  static bool _notificationsEnabled = true;

  /// Inicializa el servicio de notificaciones
  static Future<void> initialize() async {
    if (_initialized) return;

    // Inicializar timezone
    tz.initializeTimeZones();

    // Configuración para Android
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    
    // Configuración para iOS
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    // Configuración general
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    // Inicializar
    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Solicitar permisos
    await _requestPermissions();
    
    _initialized = true;
    debugPrint('🔔 NotificationService initialized');
  }

  /// Solicita permisos de notificación
  static Future<void> _requestPermissions() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>();
      
      await androidPlugin?.requestNotificationsPermission();
      await androidPlugin?.requestExactAlarmsPermission();
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      final iosPlugin = _notifications.resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin>();
      
      await iosPlugin?.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
    }
  }

  /// Maneja cuando se toca una notificación
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('🔔 Notification tapped: ${response.payload}');
    // Aquí puedes agregar navegación específica según el payload
  }

  /// Muestra una notificación inmediata
  static Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.defaultPriority,
  }) async {
    if (!_notificationsEnabled) return;

    const androidDetails = AndroidNotificationDetails(
      'mtto_60_channel',
      'MTTO 60 Notifications',
      channelDescription: 'Notificaciones de la aplicación MTTO 60',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(id, title, body, details, payload: payload);
  }

  /// Programa una notificación para más tarde
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    if (!_notificationsEnabled) return;

    const androidDetails = AndroidNotificationDetails(
      'mtto_60_scheduled',
      'MTTO 60 Scheduled',
      channelDescription: 'Notificaciones programadas de MTTO 60',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      details,
      payload: payload,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  /// Cancela una notificación específica
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  /// Cancela todas las notificaciones
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  /// Obtiene notificaciones pendientes
  static Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  /// Habilita/deshabilita notificaciones
  static void setNotificationsEnabled(bool enabled) {
    _notificationsEnabled = enabled;
    debugPrint('🔔 Notifications ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Verifica si las notificaciones están habilitadas
  static bool get notificationsEnabled => _notificationsEnabled;

  // Métodos específicos para la aplicación MTTO 60

  /// Notificación de nuevo reporte
  static Future<void> notifyNewReport({
    required String reportTitle,
    required String assetName,
    required String priority,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: '📋 Nuevo Reporte Creado',
      body: '$reportTitle - $assetName (Prioridad: $priority)',
      payload: 'new_report',
      priority: _getPriorityFromString(priority),
    );
  }

  /// Notificación de reporte asignado
  static Future<void> notifyReportAssigned({
    required String reportTitle,
    required String assignedTo,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: '👤 Reporte Asignado',
      body: '$reportTitle ha sido asignado a $assignedTo',
      payload: 'report_assigned',
    );
  }

  /// Notificación de mantenimiento programado
  static Future<void> notifyScheduledMaintenance({
    required String assetName,
    required DateTime scheduledDate,
  }) async {
    await scheduleNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: '🔧 Mantenimiento Programado',
      body: 'Mantenimiento programado para $assetName',
      scheduledDate: scheduledDate.subtract(const Duration(hours: 24)), // 24h antes
      payload: 'scheduled_maintenance',
    );
  }

  /// Notificación de reporte vencido
  static Future<void> notifyOverdueReport({
    required String reportTitle,
    required int daysOverdue,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: '⚠️ Reporte Vencido',
      body: '$reportTitle lleva $daysOverdue días sin resolver',
      payload: 'overdue_report',
      priority: NotificationPriority.high,
    );
  }

  /// Convierte string de prioridad a NotificationPriority
  static NotificationPriority _getPriorityFromString(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return NotificationPriority.max;
      case 'alta':
        return NotificationPriority.high;
      case 'media':
        return NotificationPriority.defaultPriority;
      case 'baja':
        return NotificationPriority.low;
      default:
        return NotificationPriority.defaultPriority;
    }
  }
}

enum NotificationPriority {
  min,
  low,
  defaultPriority,
  high,
  max,
}
