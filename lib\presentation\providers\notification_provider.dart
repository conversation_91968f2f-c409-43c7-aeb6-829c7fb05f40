import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import '../../core/services/notification_service.dart'; // Temporalmente comentado

class NotificationProvider extends ChangeNotifier {
  bool _notificationsEnabled = true;
  bool _reportNotifications = true;
  bool _maintenanceNotifications = true;
  bool _assignmentNotifications = true;
  bool _overdueNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  String _notificationTime = '09:00';

  // Getters
  bool get notificationsEnabled => _notificationsEnabled;
  bool get reportNotifications => _reportNotifications;
  bool get maintenanceNotifications => _maintenanceNotifications;
  bool get assignmentNotifications => _assignmentNotifications;
  bool get overdueNotifications => _overdueNotifications;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
  String get notificationTime => _notificationTime;

  /// Inicializa el provider cargando configuraciones guardadas
  Future<void> initialize() async {
    await _loadSettings();
    // await NotificationService.initialize(); // Temporalmente comentado
    notifyListeners();
  }

  /// Carga configuraciones desde SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      _reportNotifications = prefs.getBool('report_notifications') ?? true;
      _maintenanceNotifications =
          prefs.getBool('maintenance_notifications') ?? true;
      _assignmentNotifications =
          prefs.getBool('assignment_notifications') ?? true;
      _overdueNotifications = prefs.getBool('overdue_notifications') ?? true;
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
      _notificationTime = prefs.getString('notification_time') ?? '09:00';

      // Aplicar configuración al servicio
      // NotificationService.setNotificationsEnabled(_notificationsEnabled); // Temporalmente comentado

      debugPrint('🔔 Notification settings loaded');
    } catch (e) {
      debugPrint('❌ Error loading notification settings: $e');
    }
  }

  /// Guarda configuraciones en SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setBool('notifications_enabled', _notificationsEnabled);
      await prefs.setBool('report_notifications', _reportNotifications);
      await prefs.setBool(
        'maintenance_notifications',
        _maintenanceNotifications,
      );
      await prefs.setBool('assignment_notifications', _assignmentNotifications);
      await prefs.setBool('overdue_notifications', _overdueNotifications);
      await prefs.setBool('sound_enabled', _soundEnabled);
      await prefs.setBool('vibration_enabled', _vibrationEnabled);
      await prefs.setString('notification_time', _notificationTime);

      debugPrint('🔔 Notification settings saved');
    } catch (e) {
      debugPrint('❌ Error saving notification settings: $e');
    }
  }

  /// Habilita/deshabilita todas las notificaciones
  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled = enabled;
    // NotificationService.setNotificationsEnabled(enabled); // Temporalmente comentado

    if (!enabled) {
      // await NotificationService.cancelAllNotifications(); // Temporalmente comentado
    }

    await _saveSettings();
    notifyListeners();
  }

  /// Habilita/deshabilita notificaciones de reportes
  Future<void> setReportNotifications(bool enabled) async {
    _reportNotifications = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// Habilita/deshabilita notificaciones de mantenimiento
  Future<void> setMaintenanceNotifications(bool enabled) async {
    _maintenanceNotifications = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// Habilita/deshabilita notificaciones de asignaciones
  Future<void> setAssignmentNotifications(bool enabled) async {
    _assignmentNotifications = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// Habilita/deshabilita notificaciones de reportes vencidos
  Future<void> setOverdueNotifications(bool enabled) async {
    _overdueNotifications = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// Habilita/deshabilita sonido
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// Habilita/deshabilita vibración
  Future<void> setVibrationEnabled(bool enabled) async {
    _vibrationEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// Establece la hora de notificaciones diarias
  Future<void> setNotificationTime(String time) async {
    _notificationTime = time;
    await _saveSettings();
    notifyListeners();
  }

  /// Envía notificación de prueba
  Future<void> sendTestNotification() async {
    if (!_notificationsEnabled) return;

    // Simulación de notificación de prueba
    debugPrint(
      '🧪 Notificación de prueba enviada: Las notificaciones están funcionando correctamente en MTTO 60',
    );

    // await NotificationService.showNotification(...); // Temporalmente comentado
  }

  /// Obtiene el número de notificaciones pendientes
  Future<int> getPendingNotificationsCount() async {
    // Simulación de notificaciones pendientes
    return 0; // Temporalmente retorna 0
    // final pending = await NotificationService.getPendingNotifications();
    // return pending.length;
  }

  /// Cancela todas las notificaciones
  Future<void> cancelAllNotifications() async {
    debugPrint('🔔 Todas las notificaciones han sido canceladas (simulación)');
    // await NotificationService.cancelAllNotifications(); // Temporalmente comentado
  }

  // Métodos para enviar notificaciones específicas de la app

  /// Notifica cuando se crea un nuevo reporte
  Future<void> notifyNewReport({
    required String reportTitle,
    required String assetName,
    required String priority,
  }) async {
    if (!_notificationsEnabled || !_reportNotifications) return;

    debugPrint(
      '📋 Nuevo Reporte: $reportTitle - $assetName (Prioridad: $priority)',
    );
    // await NotificationService.notifyNewReport(...); // Temporalmente comentado
  }

  /// Notifica cuando se asigna un reporte
  Future<void> notifyReportAssigned({
    required String reportTitle,
    required String assignedTo,
  }) async {
    if (!_notificationsEnabled || !_assignmentNotifications) return;

    await NotificationService.notifyReportAssigned(
      reportTitle: reportTitle,
      assignedTo: assignedTo,
    );
  }

  /// Notifica mantenimiento programado
  Future<void> notifyScheduledMaintenance({
    required String assetName,
    required DateTime scheduledDate,
  }) async {
    if (!_notificationsEnabled || !_maintenanceNotifications) return;

    await NotificationService.notifyScheduledMaintenance(
      assetName: assetName,
      scheduledDate: scheduledDate,
    );
  }

  /// Notifica reportes vencidos
  Future<void> notifyOverdueReport({
    required String reportTitle,
    required int daysOverdue,
  }) async {
    if (!_notificationsEnabled || !_overdueNotifications) return;

    await NotificationService.notifyOverdueReport(
      reportTitle: reportTitle,
      daysOverdue: daysOverdue,
    );
  }

  /// Programa notificaciones diarias de resumen
  Future<void> scheduleDailySummary() async {
    if (!_notificationsEnabled) return;

    final now = DateTime.now();
    final timeParts = _notificationTime.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);

    var scheduledDate = DateTime(now.year, now.month, now.day, hour, minute);

    // Si la hora ya pasó hoy, programar para mañana
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    await NotificationService.scheduleNotification(
      id: 100000, // ID fijo para el resumen diario
      title: '📊 Resumen Diario MTTO 60',
      body: 'Revisa el estado de tus reportes y mantenimientos',
      scheduledDate: scheduledDate,
      payload: 'daily_summary',
    );
  }
}
