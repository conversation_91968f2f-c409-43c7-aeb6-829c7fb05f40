class AssetModel {
  final String id;
  final String name;
  final String code;
  final String type;
  final String? description;
  final String? location;
  final String status;
  final String? brand;
  final String? model;
  final String? serialNumber;
  final DateTime? purchaseDate;
  final double? purchasePrice;
  final String? imageUrl;
  final String? qrCode;
  final Map<String, dynamic>? customFields;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String createdBy;

  const AssetModel({
    required this.id,
    required this.name,
    required this.code,
    required this.type,
    this.description,
    this.location,
    required this.status,
    this.brand,
    this.model,
    this.serialNumber,
    this.purchaseDate,
    this.purchasePrice,
    this.imageUrl,
    this.qrCode,
    this.customFields,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
  });

  factory AssetModel.fromJson(Map<String, dynamic> json) {
    return AssetModel(
      id: json['id'] as String,
      name: json['name'] as String,
      code: json['code'] as String,
      type: json['type'] as String,
      description: json['description'] as String?,
      location: json['location'] as String?,
      status: json['status'] as String,
      brand: json['brand'] as String?,
      model: json['model'] as String?,
      serialNumber: json['serial_number'] as String?,
      purchaseDate: json['purchase_date'] != null 
          ? DateTime.parse(json['purchase_date'] as String) 
          : null,
      purchasePrice: json['purchase_price']?.toDouble(),
      imageUrl: json['image_url'] as String?,
      qrCode: json['qr_code'] as String?,
      customFields: json['custom_fields'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
      createdBy: json['created_by'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'type': type,
      'description': description,
      'location': location,
      'status': status,
      'brand': brand,
      'model': model,
      'serial_number': serialNumber,
      'purchase_date': purchaseDate?.toIso8601String(),
      'purchase_price': purchasePrice,
      'image_url': imageUrl,
      'qr_code': qrCode,
      'custom_fields': customFields,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'created_by': createdBy,
    };
  }

  AssetModel copyWith({
    String? id,
    String? name,
    String? code,
    String? type,
    String? description,
    String? location,
    String? status,
    String? brand,
    String? model,
    String? serialNumber,
    DateTime? purchaseDate,
    double? purchasePrice,
    String? imageUrl,
    String? qrCode,
    Map<String, dynamic>? customFields,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return AssetModel(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      type: type ?? this.type,
      description: description ?? this.description,
      location: location ?? this.location,
      status: status ?? this.status,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      serialNumber: serialNumber ?? this.serialNumber,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      imageUrl: imageUrl ?? this.imageUrl,
      qrCode: qrCode ?? this.qrCode,
      customFields: customFields ?? this.customFields,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  @override
  String toString() {
    return 'AssetModel(id: $id, name: $name, code: $code, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AssetModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
