import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:local_auth/local_auth.dart';
import '../../../core/constants/app_colors.dart';

class SecuritySettingsScreen extends StatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  State<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends State<SecuritySettingsScreen> {
  bool _biometricEnabled = false;
  bool _autoLockEnabled = true;
  String _autoLockTime = '5 minutos';
  bool _sessionTimeoutEnabled = true;
  String _sessionTimeout = '30 minutos';

  final LocalAuthentication _localAuth = LocalAuthentication();
  bool _biometricAvailable = false;

  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _checkBiometricAvailability();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _biometricEnabled = prefs.getBool('biometric_enabled') ?? false;
      _autoLockEnabled = prefs.getBool('auto_lock_enabled') ?? true;
      _autoLockTime = prefs.getString('auto_lock_time') ?? '5 minutos';
      _sessionTimeoutEnabled = prefs.getBool('session_timeout_enabled') ?? true;
      _sessionTimeout = prefs.getString('session_timeout') ?? '30 minutos';
    });
  }

  Future<void> _checkBiometricAvailability() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      setState(() {
        _biometricAvailable = isAvailable && isDeviceSupported;
      });
    } catch (e) {
      setState(() {
        _biometricAvailable = false;
      });
    }
  }

  Future<void> _enableBiometric() async {
    try {
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Autentícate para habilitar el acceso biométrico',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (didAuthenticate) {
        setState(() {
          _biometricEnabled = true;
        });
        _saveSettings();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Autenticación biométrica habilitada'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al habilitar biometría: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _handleBiometricToggle(bool value) {
    if (value) {
      _enableBiometric();
    } else {
      setState(() {
        _biometricEnabled = false;
      });
      _saveSettings();
    }
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('biometric_enabled', _biometricEnabled);
    await prefs.setBool('auto_lock_enabled', _autoLockEnabled);
    await prefs.setString('auto_lock_time', _autoLockTime);
    await prefs.setBool('session_timeout_enabled', _sessionTimeoutEnabled);
    await prefs.setString('session_timeout', _sessionTimeout);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Configuración de seguridad guardada'),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Seguridad'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Cambio de contraseña
            _buildPasswordSection(),
            const SizedBox(height: 24),

            // Autenticación biométrica
            _buildBiometricSection(),
            const SizedBox(height: 24),

            // Configuración de bloqueo automático
            _buildAutoLockSection(),
            const SizedBox(height: 24),

            // Configuración de sesión
            _buildSessionSection(),
            const SizedBox(height: 24),

            // Actividad de la cuenta
            _buildActivitySection(),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Contraseña',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.lock, color: AppColors.primary),
            ),
            title: const Text('Cambiar Contraseña'),
            subtitle: const Text('Actualizar tu contraseña de acceso'),
            trailing: const Icon(Icons.chevron_right),
            onTap: _showChangePasswordDialog,
          ),
          const Divider(height: 24),

          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.history, color: AppColors.warning),
            ),
            title: const Text('Historial de Contraseñas'),
            subtitle: const Text('Ver cambios recientes de contraseña'),
            trailing: const Icon(Icons.chevron_right),
            onTap: _showPasswordHistory,
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Autenticación Biométrica',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          _buildSwitchTile(
            title: 'Huella Dactilar / Face ID',
            subtitle: _biometricAvailable
                ? 'Usar biometría para acceder a la aplicación'
                : 'Biometría no disponible en este dispositivo',
            value: _biometricEnabled && _biometricAvailable,
            onChanged: _biometricAvailable ? _handleBiometricToggle : null,
          ),
        ],
      ),
    );
  }

  Widget _buildAutoLockSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Bloqueo Automático',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          _buildSwitchTile(
            title: 'Bloqueo Automático',
            subtitle: 'Bloquear la app después de un tiempo de inactividad',
            value: _autoLockEnabled,
            onChanged: (value) {
              setState(() {
                _autoLockEnabled = value;
              });
              _saveSettings();
            },
          ),
          if (_autoLockEnabled) ...[
            const Divider(height: 24),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.timer, color: AppColors.info),
              ),
              title: const Text('Tiempo de Bloqueo'),
              subtitle: Text('Bloquear después de $_autoLockTime'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _selectAutoLockTime,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSessionSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Gestión de Sesión',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          _buildSwitchTile(
            title: 'Expiración de Sesión',
            subtitle: 'Cerrar sesión automáticamente por inactividad',
            value: _sessionTimeoutEnabled,
            onChanged: (value) {
              setState(() {
                _sessionTimeoutEnabled = value;
              });
              _saveSettings();
            },
          ),
          if (_sessionTimeoutEnabled) ...[
            const Divider(height: 24),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.logout, color: AppColors.warning),
              ),
              title: const Text('Tiempo de Expiración'),
              subtitle: Text('Cerrar sesión después de $_sessionTimeout'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _selectSessionTimeout,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActivitySection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Actividad de la Cuenta',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.devices, color: AppColors.success),
            ),
            title: const Text('Dispositivos Activos'),
            subtitle: const Text('Ver dispositivos con sesión iniciada'),
            trailing: const Icon(Icons.chevron_right),
            onTap: _showActiveDevices,
          ),
          const Divider(height: 24),

          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.history, color: AppColors.info),
            ),
            title: const Text('Historial de Acceso'),
            subtitle: const Text('Ver intentos de acceso recientes'),
            trailing: const Icon(Icons.chevron_right),
            onTap: _showAccessHistory,
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: AppColors.primary,
        ),
      ],
    );
  }

  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cambiar Contraseña'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _currentPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Contraseña Actual',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _newPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Nueva Contraseña',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _confirmPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Confirmar Nueva Contraseña',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: _changePassword,
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary),
            child: const Text('Cambiar', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _changePassword() {
    // TODO: Implementar cambio de contraseña real
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Contraseña cambiada exitosamente'),
        backgroundColor: AppColors.success,
      ),
    );
    _currentPasswordController.clear();
    _newPasswordController.clear();
    _confirmPasswordController.clear();
  }

  void _selectAutoLockTime() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tiempo de Bloqueo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children:
              [
                    '1 minuto',
                    '5 minutos',
                    '10 minutos',
                    '15 minutos',
                    '30 minutos',
                  ]
                  .map(
                    (time) => RadioListTile<String>(
                      title: Text(time),
                      value: time,
                      groupValue: _autoLockTime,
                      onChanged: (value) {
                        setState(() {
                          _autoLockTime = value!;
                        });
                        Navigator.of(context).pop();
                        _saveSettings();
                      },
                    ),
                  )
                  .toList(),
        ),
      ),
    );
  }

  void _selectSessionTimeout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tiempo de Expiración'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ['15 minutos', '30 minutos', '1 hora', '2 horas', '4 horas']
              .map(
                (time) => RadioListTile<String>(
                  title: Text(time),
                  value: time,
                  groupValue: _sessionTimeout,
                  onChanged: (value) {
                    setState(() {
                      _sessionTimeout = value!;
                    });
                    Navigator.of(context).pop();
                    _saveSettings();
                  },
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  void _showPasswordHistory() {
    // TODO: Implementar historial de contraseñas
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Historial de contraseñas próximamente'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showActiveDevices() {
    // TODO: Implementar lista de dispositivos activos
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Dispositivos activos próximamente'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showAccessHistory() {
    // TODO: Implementar historial de acceso
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Historial de acceso próximamente'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
