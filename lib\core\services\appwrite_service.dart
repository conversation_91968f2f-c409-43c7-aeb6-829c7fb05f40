// AppwriteService temporalmente deshabilitado debido a problemas de compatibilidad en Android
// Para habilitar: descomentar appwrite en pubspec.yaml y este archivo

/*
import 'package:flutter/foundation.dart';
import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart' as models;
import '../constants/app_constants.dart';
import 'mock_appwrite_service.dart';

class AppwriteService {
  static AppwriteService? _instance;
  late Client _client;
  late Account _account;
  late Databases _databases;
  late Storage _storage;
  late Realtime _realtime;

  AppwriteService._internal() {
    _client = Client();
    _client
        .setEndpoint(AppConstants.appwriteEndpoint)
        .setProject(AppConstants.appwriteProjectId);

    // Solo para desarrollo local con certificados auto-firmados
    // Comentar en producción
    // .setSelfSigned(status: true);

    _account = Account(_client);
    _databases = Databases(_client);
    _storage = Storage(_client);
    _realtime = Realtime(_client);
  }

  static AppwriteService get instance {
    _instance ??= AppwriteService._internal();
    return _instance!;
  }

  // Getters para acceder a los servicios
  Client get client => _client;
  Account get account => _account;
  Databases get databases => _databases;
  Storage get storage => _storage;
  Realtime get realtime => _realtime;

  // Método para inicializar el servicio
  static Future<void> initialize() async {
    AppwriteService.instance;
  }

  // Método para verificar la sesión actual
  Future<bool> isLoggedIn() async {
    try {
      await _account.get();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Método para obtener el usuario actual
  Future<models.User?> getCurrentUser() async {
    try {
      return await _account.get();
    } catch (e) {
      return null;
    }
  }
}
*/

// Placeholder class para evitar errores de importación
class AppwriteService {
  static AppwriteService? _instance;

  static AppwriteService get instance {
    _instance ??= AppwriteService._internal();
    return _instance!;
  }

  AppwriteService._internal();

  static Future<void> initialize() async {
    // Placeholder - usar MockAppwriteService en su lugar
  }
}
