import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

class PdfService {
  static Future<File> generateReportPdf({
    required String reportType,
    required String reporterName,
    required String reporterPosition,
    required String location,
    required String category,
    required String assetName,
    required String codeOrPlate,
    required String priority,
    required List<FaultData> faults,
    required String? horometer,
    required String? kilometrage,
    String? vehicleType,
    String? unitName,
    String? provider,
  }) async {
    final pdf = pw.Document();
    final now = DateTime.now();
    final dateFormatter = DateFormat('dd/MM/yyyy');
    final timeFormatter = DateFormat('HH:mm:ss');

    // Generar número de reporte único
    final reportNumber =
        'N2SET-${now.millisecondsSinceEpoch.toString().substring(8)}';

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Encabezado
              _buildHeader(reportType, reportNumber, dateFormatter.format(now)),
              pw.SizedBox(height: 20),

              // Información del reporte
              _buildReportInfo(
                reporterName: reporterName,
                reporterPosition: reporterPosition,
                location: location,
                codeOrPlate: codeOrPlate,
                date: dateFormatter.format(now),
                time: timeFormatter.format(now),
              ),
              pw.SizedBox(height: 20),

              // Clasificación del activo
              _buildAssetClassification(
                category: category,
                assetName: assetName,
                vehicleType: vehicleType,
                unitName: unitName,
                provider: provider,
                horometer: horometer,
                kilometrage: kilometrage,
              ),
              pw.SizedBox(height: 20),

              // Nivel de urgencia
              _buildUrgencyLevel(priority),
              pw.SizedBox(height: 20),

              // Descripción de fallas
              _buildFaultsDescription(faults),
              pw.SizedBox(height: 20),

              // Sistemas afectados
              _buildAffectedSystems(faults),
              pw.SizedBox(height: 20),

              // Observaciones
              _buildObservations(),
              pw.SizedBox(height: 20),

              // Firmas
              _buildSignatures(),
              pw.SizedBox(height: 20),

              // Pie de página
              _buildFooter(
                reportNumber,
                dateFormatter.format(now),
                timeFormatter.format(now),
              ),
            ],
          );
        },
      ),
    );

    // Guardar el PDF
    final output = await getApplicationDocumentsDirectory();
    final file = File('${output.path}/reporte_$reportNumber.pdf');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  static pw.Widget _buildHeader(
    String reportType,
    String reportNumber,
    String date,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black, width: 2),
      ),
      child: pw.Row(
        children: [
          // Logo (placeholder)
          pw.Container(
            width: 80,
            height: 60,
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.black),
            ),
            child: pw.Center(
              child: pw.Text(
                'NEXT\nVIEW',
                style: pw.TextStyle(
                  fontSize: 10,
                  fontWeight: pw.FontWeight.bold,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ),
          pw.SizedBox(width: 20),

          // Título principal
          pw.Expanded(
            child: pw.Center(
              child: pw.Text(
                reportType.toUpperCase(),
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
          ),

          // Información de versión y fecha
          pw.Container(
            width: 120,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text('Versión: 002', style: const pw.TextStyle(fontSize: 8)),
                pw.Text('Fecha: $date', style: const pw.TextStyle(fontSize: 8)),
                pw.Text(
                  'Código: CO-F-01-09',
                  style: const pw.TextStyle(fontSize: 8),
                ),
                pw.Text(
                  'Celular: 3118406413',
                  style: const pw.TextStyle(fontSize: 8),
                ),
                pw.Text(
                  'HOROMETRO: KILOMETRAJE',
                  style: const pw.TextStyle(fontSize: 8),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildReportInfo({
    required String reporterName,
    required String reporterPosition,
    required String location,
    required String codeOrPlate,
    required String date,
    required String time,
  }) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        children: [
          // Primera fila
          pw.Row(
            children: [
              _buildInfoCell('QUIEN REPORTA:', reporterName, flex: 3),
              _buildInfoCell('CARGO:', reporterPosition, flex: 2),
              _buildInfoCell('CELULAR:', '3118406413', flex: 2),
            ],
          ),
          // Segunda fila
          pw.Row(
            children: [
              _buildInfoCell('UBICACIÓN:', location, flex: 3),
              _buildInfoCell('PLACA/\nCÓDIGO:', codeOrPlate, flex: 2),
              _buildInfoCell('FECHA:', date, flex: 2),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInfoCell(String label, String value, {int flex = 1}) {
    return pw.Expanded(
      flex: flex,
      child: pw.Container(
        height: 30,
        padding: const pw.EdgeInsets.all(4),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.black, width: 0.5),
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              label,
              style: pw.TextStyle(fontSize: 8, fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(value, style: const pw.TextStyle(fontSize: 10)),
          ],
        ),
      ),
    );
  }

  static pw.Widget _buildAssetClassification({
    required String category,
    required String assetName,
    String? vehicleType,
    String? unitName,
    String? provider,
    String? horometer,
    String? kilometrage,
  }) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        children: [
          // Checkboxes de categoría
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Row(
              children: [
                _buildCheckbox(
                  'UNIDAD',
                  category == 'Vehículos' && vehicleType == 'Unidades',
                ),
                pw.SizedBox(width: 20),
                _buildCheckbox(
                  'CAMIONETA',
                  category == 'Vehículos' && vehicleType == 'Camionetas',
                ),
                pw.SizedBox(width: 20),
                _buildCheckbox('HERRAMIENTA', category == 'Herramienta'),
                pw.SizedBox(width: 20),
                _buildCheckbox(
                  'INFRAESTRUCTURA',
                  category == 'Infraestructura',
                ),
                pw.SizedBox(width: 20),
                _buildCheckbox('EQUIPO', category == 'Equipo'),
                pw.SizedBox(width: 20),
                _buildCheckbox('OTRA', false),
              ],
            ),
          ),
          // Información específica
          pw.Row(
            children: [
              pw.Expanded(
                child: pw.Container(
                  padding: const pw.EdgeInsets.all(4),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 0.5),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'CUAL/NOMBRE:',
                        style: pw.TextStyle(
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        unitName ?? assetName,
                        style: const pw.TextStyle(fontSize: 10),
                      ),
                    ],
                  ),
                ),
              ),
              if (horometer != null || kilometrage != null) ...[
                pw.Container(
                  width: 80,
                  padding: const pw.EdgeInsets.all(4),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 0.5),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'HOROMETRO',
                        style: pw.TextStyle(
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        horometer ?? '',
                        style: const pw.TextStyle(fontSize: 10),
                      ),
                    ],
                  ),
                ),
                pw.Container(
                  width: 80,
                  padding: const pw.EdgeInsets.all(4),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 0.5),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'KILOMETRAJE',
                        style: pw.TextStyle(
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        kilometrage ?? '',
                        style: const pw.TextStyle(fontSize: 10),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildCheckbox(String label, bool isChecked) {
    return pw.Row(
      mainAxisSize: pw.MainAxisSize.min,
      children: [
        pw.Container(
          width: 12,
          height: 12,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black),
          ),
          child: isChecked
              ? pw.Center(
                  child: pw.Text(
                    'X',
                    style: pw.TextStyle(
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                )
              : null,
        ),
        pw.SizedBox(width: 4),
        pw.Text(label, style: const pw.TextStyle(fontSize: 8)),
      ],
    );
  }

  static pw.Widget _buildUrgencyLevel(String priority) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Row(
        children: [
          pw.Container(
            width: 100,
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'NIVEL DE URGENCIA',
              style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Expanded(
            child: pw.Row(
              children: [
                _buildCheckbox('Benigno', priority == 'Baja'),
                pw.SizedBox(width: 20),
                _buildCheckbox('Moderado', priority == 'Media'),
                pw.SizedBox(width: 20),
                _buildCheckbox('Avanzado', priority == 'Alta'),
                pw.SizedBox(width: 20),
                _buildCheckbox('Crítico', priority == 'Crítica'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFaultsDescription(List<FaultData> faults) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey300,
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
            ),
            child: pw.Text(
              'DESCRIPCIÓN DE LA SOLICITUD',
              style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Container(
            height: 200,
            padding: const pw.EdgeInsets.all(8),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                for (int i = 0; i < faults.length && i < 10; i++) ...[
                  pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Container(
                        width: 20,
                        child: pw.Text(
                          '${i + 1}.',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Text(
                          faults[i].description,
                          style: const pw.TextStyle(fontSize: 10),
                        ),
                      ),
                      // Columnas adicionales del formato original
                      pw.Container(
                        width: 60,
                        height: 20,
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(
                            color: PdfColors.black,
                            width: 0.5,
                          ),
                        ),
                        child: pw.Center(
                          child: pw.Text(
                            faults[i].system?.substring(0, 2).toUpperCase() ??
                                'SS',
                            style: const pw.TextStyle(fontSize: 8),
                          ),
                        ),
                      ),
                      pw.SizedBox(width: 5),
                      pw.Container(
                        width: 40,
                        height: 20,
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(
                            color: PdfColors.black,
                            width: 0.5,
                          ),
                        ),
                        child: pw.Center(
                          child: pw.Text(
                            'No',
                            style: const pw.TextStyle(fontSize: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 8),
                ],
                // Líneas vacías para completar hasta 10
                for (int i = faults.length; i < 10; i++) ...[
                  pw.Row(
                    children: [
                      pw.Container(
                        width: 20,
                        child: pw.Text(
                          '${i + 1}.',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Container(
                          height: 15,
                          decoration: pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(
                                color: PdfColors.black,
                                width: 0.5,
                              ),
                            ),
                          ),
                        ),
                      ),
                      pw.Container(
                        width: 60,
                        height: 15,
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(
                            color: PdfColors.black,
                            width: 0.5,
                          ),
                        ),
                      ),
                      pw.SizedBox(width: 5),
                      pw.Container(
                        width: 40,
                        height: 15,
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(
                            color: PdfColors.black,
                            width: 0.5,
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 8),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildAffectedSystems(List<FaultData> faults) {
    // Obtener sistemas únicos de las fallas
    final systems = faults.map((f) => f.system).where((s) => s != null).toSet();

    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        children: [
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey300,
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
            ),
            child: pw.Text(
              'SISTEMA DONDE SE PRESENTA LA SOLICITUD DE TRABAJO',
              style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Wrap(
              spacing: 20,
              runSpacing: 8,
              children: [
                _buildCheckbox(
                  'CA: Cabina y accesorios',
                  systems.contains('Cabina y Accesorios'),
                ),
                _buildCheckbox(
                  'SW: Slickline/Wireline',
                  systems.contains('Slickline / Wireline'),
                ),
                _buildCheckbox(
                  'SS: Sistema de Suspensión',
                  systems.contains('Sistema de Suspensión'),
                ),
                _buildCheckbox(
                  'SE: Sistema Eléctrico',
                  systems.contains('Sistema Eléctrico'),
                ),
                _buildCheckbox('GR: Grúa', systems.contains('Grúa')),
                _buildCheckbox('MT: Motor', systems.contains('Motor')),
                _buildCheckbox(
                  'SH: Sistema Hidráulico',
                  systems.contains('Sistema Hidráulico'),
                ),
                _buildCheckbox(
                  'SD: Sistema de Dirección',
                  systems.contains('Sistema de Dirección'),
                ),
                _buildCheckbox(
                  'SF: Sistema de Frenos',
                  systems.contains('Sistema de Frenos'),
                ),
                _buildCheckbox(
                  'TP: Transmisión y Potencia',
                  systems.contains('Transmisión y Potencia'),
                ),
                _buildCheckbox('TO: Torre', systems.contains('Torre')),
                _buildCheckbox(
                  'SM: Sistema de Medición',
                  systems.contains('Sistema de Medición'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildObservations() {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey300,
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
            ),
            child: pw.Text(
              'OBSERVACIONES:',
              style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Container(
            height: 60,
            width: double.infinity,
            padding: const pw.EdgeInsets.all(8),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSignatures() {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        children: [
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey300,
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
            ),
            child: pw.Text(
              'USO EXCLUSIVO DE MANTENIMIENTO',
              style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Row(
            children: [
              pw.Expanded(
                child: pw.Container(
                  height: 60,
                  padding: const pw.EdgeInsets.all(8),
                  decoration: pw.BoxDecoration(
                    border: pw.Border(
                      right: pw.BorderSide(color: PdfColors.black, width: 0.5),
                    ),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'LÍDER DE MANTENIMIENTO',
                        style: pw.TextStyle(
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 20),
                      pw.Text(
                        'FIRMA',
                        style: pw.TextStyle(
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'FECHA',
                        style: pw.TextStyle(
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              pw.Expanded(
                child: pw.Container(
                  height: 60,
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'INSTRUCCIONES:',
                        style: pw.TextStyle(
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '1) El reporte de OT se llena cada vez que se requiera de área de mantenimiento correctivo.\n'
                        '2) Debe ser llenado por el Jefe de Operaciones, Jefes, Líderes, Operadores, Técnicos que tengan información de las\n'
                        'especificaciones de la calidad de trabajo.\n'
                        '3) Todos los reportes de falla de herramientas, equipos integrados del reporte de electromecánico. Los reportes de falla se\n'
                        'deben entregar al Jefe de Operaciones.\n'
                        '4) Los reportes de falla se van validando dentro del área de electromecánico del líder de operaciones (Director, coordinador, supervisor\n'
                        'asignado de operaciones).',
                        style: const pw.TextStyle(fontSize: 6),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(String reportNumber, String date, String time) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text('$date - $time', style: const pw.TextStyle(fontSize: 8)),
          pw.Text(reportNumber, style: const pw.TextStyle(fontSize: 8)),
        ],
      ),
    );
  }

  // Método para mostrar el PDF generado
  static Future<void> showPdf(File pdfFile) async {
    final bytes = await pdfFile.readAsBytes();
    await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => bytes);
  }

  // Método para compartir el PDF
  static Future<void> sharePdf(File pdfFile) async {
    await Printing.sharePdf(
      bytes: await pdfFile.readAsBytes(),
      filename: pdfFile.path.split('/').last,
    );
  }
}

// Clase para datos de falla
class FaultData {
  final String title;
  final String description;
  final String? system;
  final List<String> imagePaths;

  FaultData({
    required this.title,
    required this.description,
    this.system,
    this.imagePaths = const [],
  });
}
