import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import 'package:share_plus/share_plus.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/app_utils.dart';
import '../../../data/models/report_model.dart';
import '../../../core/services/enhanced_pdf_service.dart';
import 'pdf_viewer_screen.dart';

class PDFPreviewScreen extends StatefulWidget {
  final ReportModel report;
  final String? pdfPath;

  const PDFPreviewScreen({super.key, required this.report, this.pdfPath});

  @override
  State<PDFPreviewScreen> createState() => _PDFPreviewScreenState();
}

class _PDFPreviewScreenState extends State<PDFPreviewScreen> {
  bool _isGenerating = false;
  String? _generatedPdfPath;

  @override
  void initState() {
    super.initState();
    _generatedPdfPath = widget.pdfPath;
    if (_generatedPdfPath == null) {
      _generatePDF();
    }
  }

  Future<void> _generatePDF() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      // Generar PDF real usando el servicio mejorado
      final pdfFile = await EnhancedPdfService.generateReportPdf(widget.report);

      setState(() {
        _generatedPdfPath = pdfFile.path;
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _isGenerating = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'Error al generar PDF: $e');
      }
    }
  }

  Future<void> _downloadPDF() async {
    if (_generatedPdfPath == null) return;

    try {
      if (kIsWeb) {
        // En web, simular descarga
        if (mounted) {
          AppUtils.showSnackBar(
            context,
            'Funcionalidad de descarga de PDF disponible en la aplicación móvil',
          );
        }
      } else {
        // En móvil, compartir el archivo PDF real
        await _shareRealPDF();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'Error al ${kIsWeb ? "abrir" : "compartir"} PDF: $e',
        );
      }
    }
  }

  Future<void> _shareRealPDF() async {
    // Mostrar proceso de preparación
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Preparando PDF para compartir...'),
          ],
        ),
      ),
    );

    // Pequeña pausa para mostrar el loading
    await Future.delayed(const Duration(milliseconds: 500));

    if (mounted) {
      Navigator.of(context).pop(); // Cerrar diálogo de carga

      // Compartir el archivo PDF real
      try {
        await Share.shareXFiles(
          [XFile(_generatedPdfPath!)],
          text: 'Reporte de Mantenimiento: ${widget.report.title}',
          subject: 'Reporte MTTO 60: ${widget.report.title}',
        );

        if (mounted) {
          AppUtils.showSnackBar(context, 'PDF compartido exitosamente');
        }
      } catch (e) {
        if (mounted) {
          AppUtils.showSnackBar(context, 'Error al compartir PDF: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Vista Previa PDF'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_generatedPdfPath != null)
            IconButton(
              onPressed: _downloadPDF,
              icon: Icon(kIsWeb ? Icons.open_in_new : Icons.share),
              tooltip: kIsWeb ? 'Abrir PDF' : 'Compartir PDF',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isGenerating) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Generando PDF...'),
          ],
        ),
      );
    }

    if (_generatedPdfPath == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: AppColors.error),
            const SizedBox(height: 16),
            const Text('Error al generar el PDF'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _generatePDF,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: const Text(
                'Reintentar',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Información del reporte
          _buildReportInfo(),
          const SizedBox(height: 24),

          // Vista previa del contenido
          _buildContentPreview(),
          const SizedBox(height: 24),

          // Acciones
          _buildActions(),
        ],
      ),
    );
  }

  Widget _buildReportInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.picture_as_pdf, color: AppColors.error),
                const SizedBox(width: 8),
                const Text(
                  'Información del PDF',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Título:', widget.report.title),
            _buildInfoRow('Código:', widget.report.assetId),
            _buildInfoRow(
              'Fecha:',
              AppUtils.formatDate(widget.report.reportedAt),
            ),
            _buildInfoRow('Estado:', widget.report.status),
            _buildInfoRow('Prioridad:', widget.report.priority),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildContentPreview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.preview, color: AppColors.info),
                SizedBox(width: 8),
                Text(
                  'Vista Previa del Contenido',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Descripción
            if (widget.report.description.isNotEmpty) ...[
              const Text(
                'Descripción:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(widget.report.description),
              ),
              const SizedBox(height: 16),
            ],

            // Descripción del reporte (simulando fallas)
            if (widget.report.description.isNotEmpty) ...[
              const Text(
                'Descripción del Problema:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Problema: ${widget.report.description}',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    Text('Prioridad: ${widget.report.priority}'),
                    Text('Tipo: ${widget.report.type}'),
                  ],
                ),
              ),
            ],

            // Nota sobre imágenes
            if (widget.report.imageUrls.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.info, color: AppColors.info),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Este reporte incluye ${widget.report.imageUrls.length} imagen(es) adjunta(s)',
                        style: const TextStyle(color: AppColors.info),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.download, color: AppColors.success),
                SizedBox(width: 8),
                Text(
                  'Acciones',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Visualizar PDF
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _viewPDF,
                icon: const Icon(Icons.visibility),
                label: const Text('Visualizar PDF'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.info,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Compartir PDF
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _downloadPDF,
                icon: Icon(kIsWeb ? Icons.open_in_new : Icons.share),
                label: Text(kIsWeb ? 'Abrir PDF' : 'Compartir PDF'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Regenerar PDF
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _generatePDF,
                icon: const Icon(Icons.refresh),
                label: const Text('Regenerar PDF'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _viewPDF() async {
    if (_generatedPdfPath == null) {
      AppUtils.showSnackBar(context, 'Primero debe generar el PDF');
      return;
    }

    try {
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PDFViewerScreen(
            pdfPath: _generatedPdfPath!,
            title: 'Reporte: ${widget.report.title}',
          ),
        ),
      );
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'Error al abrir PDF: $e');
      }
    }
  }
}
