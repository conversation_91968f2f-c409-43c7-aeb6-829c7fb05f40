class WorkOrderModel {
  final String id;
  final String? reportId;
  final String assetId;
  final String title;
  final String description;
  final String type;
  final String priority;
  final String status;
  final String? assignedTo;
  final DateTime? scheduledDate;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final int? estimatedHours;
  final int? actualHours;
  final List<String> materials;
  final List<String> tasks;
  final String? notes;
  final List<String> imageUrls;
  final String? signatureUrl;
  final DateTime createdAt;
  final String createdBy;
  final DateTime? updatedAt;

  const WorkOrderModel({
    required this.id,
    this.reportId,
    required this.assetId,
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
    required this.status,
    this.assignedTo,
    this.scheduledDate,
    this.startedAt,
    this.completedAt,
    this.estimatedHours,
    this.actualHours,
    this.materials = const [],
    this.tasks = const [],
    this.notes,
    this.imageUrls = const [],
    this.signatureUrl,
    required this.createdAt,
    required this.createdBy,
    this.updatedAt,
  });

  factory WorkOrderModel.fromJson(Map<String, dynamic> json) {
    return WorkOrderModel(
      id: json['id'] as String,
      reportId: json['report_id'] as String?,
      assetId: json['asset_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      priority: json['priority'] as String,
      status: json['status'] as String,
      assignedTo: json['assigned_to'] as String?,
      scheduledDate: json['scheduled_date'] != null 
          ? DateTime.parse(json['scheduled_date'] as String) 
          : null,
      startedAt: json['started_at'] != null 
          ? DateTime.parse(json['started_at'] as String) 
          : null,
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at'] as String) 
          : null,
      estimatedHours: json['estimated_hours'] as int?,
      actualHours: json['actual_hours'] as int?,
      materials: json['materials'] != null 
          ? List<String>.from(json['materials'] as List)
          : [],
      tasks: json['tasks'] != null 
          ? List<String>.from(json['tasks'] as List)
          : [],
      notes: json['notes'] as String?,
      imageUrls: json['image_urls'] != null 
          ? List<String>.from(json['image_urls'] as List)
          : [],
      signatureUrl: json['signature_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      createdBy: json['created_by'] as String,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'report_id': reportId,
      'asset_id': assetId,
      'title': title,
      'description': description,
      'type': type,
      'priority': priority,
      'status': status,
      'assigned_to': assignedTo,
      'scheduled_date': scheduledDate?.toIso8601String(),
      'started_at': startedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'estimated_hours': estimatedHours,
      'actual_hours': actualHours,
      'materials': materials,
      'tasks': tasks,
      'notes': notes,
      'image_urls': imageUrls,
      'signature_url': signatureUrl,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  WorkOrderModel copyWith({
    String? id,
    String? reportId,
    String? assetId,
    String? title,
    String? description,
    String? type,
    String? priority,
    String? status,
    String? assignedTo,
    DateTime? scheduledDate,
    DateTime? startedAt,
    DateTime? completedAt,
    int? estimatedHours,
    int? actualHours,
    List<String>? materials,
    List<String>? tasks,
    String? notes,
    List<String>? imageUrls,
    String? signatureUrl,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
  }) {
    return WorkOrderModel(
      id: id ?? this.id,
      reportId: reportId ?? this.reportId,
      assetId: assetId ?? this.assetId,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      assignedTo: assignedTo ?? this.assignedTo,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      actualHours: actualHours ?? this.actualHours,
      materials: materials ?? this.materials,
      tasks: tasks ?? this.tasks,
      notes: notes ?? this.notes,
      imageUrls: imageUrls ?? this.imageUrls,
      signatureUrl: signatureUrl ?? this.signatureUrl,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'WorkOrderModel(id: $id, title: $title, type: $type, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WorkOrderModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
