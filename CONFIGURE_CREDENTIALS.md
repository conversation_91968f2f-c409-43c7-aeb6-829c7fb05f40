# 🔑 Configurar Credenciales de Appwrite

## 📋 Pasos Rápidos

### 1. Crear Proyecto en Appwrite
1. Ve a [cloud.appwrite.io](https://cloud.appwrite.io)
2. Crea cuenta y nuevo proyecto llamado "MTTO 60"
3. Copia tu **Project ID** (aparece en Settings)

### 2. Actualizar Credenciales en Flutter

Abre el archivo `lib/core/constants/app_constants.dart` y reemplaza:

```dart
static const String appwriteProjectId = 'TU_PROJECT_ID_AQUI';
```

Por:

```dart
static const String appwriteProjectId = 'tu-project-id-real';
```

### 3. Configurar Plataformas en Appwrite

**Para Flutter (Móvil):**
1. En tu proyecto Appwrite, ve a Settings → Platforms
2. Add Platform → Flutter
3. Package Name: `com.example.mtto_60`

**Para Web:**
1. Add Platform → Web
2. Hostname: `localhost` (para desarrollo)

### 4. Crear Base de Datos

1. Ve a "Databases" en Appwrite
2. Create Database
3. Database ID: `mtto_60_db`
4. Name: `MTTO 60 Database`

### 5. Probar Configuración

```bash
# Ejecutar la aplicación
flutter run

# Para web
flutter run -d chrome
```

## ✅ Verificación

Si todo está configurado correctamente, deberías ver:
- ✅ Conexión exitosa con Appwrite
- Project ID y endpoint mostrados
- Sin errores de conexión

## 🚨 Problemas Comunes

**Error "Project not found":**
- Verifica que el Project ID sea correcto
- Asegúrate de que el proyecto esté activo

**Error de CORS en web:**
- Verifica que agregaste la plataforma Web
- Hostname debe ser `localhost` para desarrollo

**Error de permisos:**
- Asegúrate de haber configurado las plataformas correctamente

## 📞 Siguiente Paso

Una vez que veas "✅ Conexión exitosa", continúa con la configuración completa siguiendo `setup_appwrite.md` para crear todas las colecciones y configurar la base de datos completa.

## 🔄 Volver a Pantallas Normales

Cuando hayas terminado las pruebas, en `lib/main.dart` cambia:

```dart
home: const AppwriteTestScreen(), // Pantalla de prueba temporal
```

Por:

```dart
home: Consumer<AuthProvider>(
  builder: (context, authProvider, _) {
    if (authProvider.isAuthenticated) {
      return const DashboardScreen();
    } else {
      return const LoginScreen();
    }
  },
),
```

¡Esto te permitirá probar la conexión antes de configurar toda la base de datos! 🚀
