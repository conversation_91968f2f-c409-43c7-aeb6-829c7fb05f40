import 'package:flutter/foundation.dart';
import '../../data/models/maintenance_model.dart';
import '../../core/services/backend_service.dart';

class MaintenanceProvider with ChangeNotifier {
  final BackendService _backend = BackendService.instance;

  List<MaintenanceModel> _maintenances = [];
  bool _isLoading = false;
  String? _errorMessage;

  List<MaintenanceModel> get maintenances => _maintenances;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Métodos CRUD
  Future<void> loadMaintenances() async {
    _setLoading(true);
    _clearError();

    try {
      // Simular datos mock para desarrollo
      await Future.delayed(const Duration(milliseconds: 500));

      _maintenances = _generateMockMaintenances();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Error al cargar mantenimientos: ${e.toString()}');
      _setLoading(false);
    }
  }

  // Obtener mantenimientos por activo
  List<MaintenanceModel> getMaintenancesByAsset(String assetId) {
    return _maintenances.where((m) => m.assetId == assetId).toList()
      ..sort((a, b) => b.scheduledDate.compareTo(a.scheduledDate));
  }

  // Obtener mantenimientos recientes
  List<MaintenanceModel> getRecentMaintenances({int limit = 5}) {
    final sortedMaintenances = List<MaintenanceModel>.from(_maintenances);
    sortedMaintenances.sort((a, b) => b.scheduledDate.compareTo(a.scheduledDate));
    return sortedMaintenances.take(limit).toList();
  }

  // Obtener mantenimientos pendientes
  List<MaintenanceModel> getPendingMaintenances() {
    return _maintenances
        .where((m) => m.status == 'Programado' || m.status == 'En Progreso')
        .toList();
  }

  // Obtener mantenimientos completados
  List<MaintenanceModel> getCompletedMaintenances() {
    return _maintenances.where((m) => m.status == 'Completado').toList();
  }

  // Generar datos mock de mantenimientos
  List<MaintenanceModel> _generateMockMaintenances() {
    final now = DateTime.now();
    return [
      // Mantenimientos para Computadora Dell Optiplex (asset_001)
      MaintenanceModel(
        id: 'maint_001',
        assetId: 'asset_001',
        assetName: 'Computadora Dell Optiplex',
        title: 'Limpieza y mantenimiento preventivo',
        description: 'Limpieza interna del equipo, verificación de ventiladores y actualización de software.',
        type: 'Preventivo',
        status: 'Completado',
        priority: 'Media',
        scheduledDate: now.subtract(const Duration(days: 30)),
        startedAt: now.subtract(const Duration(days: 30)),
        completedAt: now.subtract(const Duration(days: 30)),
        assignedTo: 'Ana Técnico IT',
        performedBy: 'Ana Técnico IT',
        notes: 'Equipo limpiado exitosamente. Se actualizó el sistema operativo y antivirus.',
        tasksCompleted: [
          'Limpieza interna del gabinete',
          'Verificación de ventiladores',
          'Actualización de Windows',
          'Actualización de antivirus',
          'Verificación de memoria RAM'
        ],
        partsUsed: ['Aire comprimido', 'Pasta térmica'],
        cost: 25.50,
        durationMinutes: 90,
        nextMaintenanceDate: 'En 3 meses',
      ),

      MaintenanceModel(
        id: 'maint_002',
        assetId: 'asset_001',
        assetName: 'Computadora Dell Optiplex',
        title: 'Reemplazo de disco duro',
        description: 'Reemplazo de disco duro mecánico por SSD para mejorar rendimiento.',
        type: 'Correctivo',
        status: 'Completado',
        priority: 'Alta',
        scheduledDate: now.subtract(const Duration(days: 60)),
        startedAt: now.subtract(const Duration(days: 60)),
        completedAt: now.subtract(const Duration(days: 60)),
        assignedTo: 'Ana Técnico IT',
        performedBy: 'Ana Técnico IT',
        notes: 'Disco duro reemplazado exitosamente. Sistema migrado sin pérdida de datos.',
        tasksCompleted: [
          'Respaldo de datos',
          'Instalación de SSD 500GB',
          'Migración del sistema',
          'Verificación de funcionamiento',
          'Configuración de arranque'
        ],
        partsUsed: ['SSD Kingston 500GB', 'Cable SATA'],
        cost: 120.00,
        durationMinutes: 180,
      ),

      // Mantenimientos para Impresora HP LaserJet (asset_002)
      MaintenanceModel(
        id: 'maint_003',
        assetId: 'asset_002',
        assetName: 'Impresora HP LaserJet',
        title: 'Mantenimiento preventivo mensual',
        description: 'Limpieza de rodillos, verificación de tóner y calibración.',
        type: 'Preventivo',
        status: 'Completado',
        priority: 'Media',
        scheduledDate: now.subtract(const Duration(days: 15)),
        startedAt: now.subtract(const Duration(days: 15)),
        completedAt: now.subtract(const Duration(days: 15)),
        assignedTo: 'Carlos Técnico',
        performedBy: 'Carlos Técnico',
        notes: 'Mantenimiento completado. Impresora funcionando correctamente.',
        tasksCompleted: [
          'Limpieza de rodillos',
          'Verificación de tóner',
          'Calibración de impresión',
          'Limpieza de sensores',
          'Prueba de impresión'
        ],
        partsUsed: ['Kit de limpieza HP'],
        cost: 35.00,
        durationMinutes: 60,
        nextMaintenanceDate: 'En 1 mes',
      ),

      // Mantenimientos para Aire Acondicionado (asset_003)
      MaintenanceModel(
        id: 'maint_004',
        assetId: 'asset_003',
        assetName: 'Aire Acondicionado Central',
        title: 'Mantenimiento trimestral',
        description: 'Limpieza de filtros, verificación de refrigerante y revisión de componentes.',
        type: 'Preventivo',
        status: 'Completado',
        priority: 'Alta',
        scheduledDate: now.subtract(const Duration(days: 7)),
        startedAt: now.subtract(const Duration(days: 7)),
        completedAt: now.subtract(const Duration(days: 6)),
        assignedTo: 'Pedro Mantenimiento',
        performedBy: 'Pedro Mantenimiento',
        notes: 'Mantenimiento trimestral completado. Sistema funcionando óptimamente.',
        tasksCompleted: [
          'Limpieza de filtros',
          'Verificación de refrigerante R-410A',
          'Revisión de compresor',
          'Limpieza de serpentines',
          'Verificación de termostato'
        ],
        partsUsed: ['Filtros HEPA', 'Refrigerante R-410A'],
        cost: 150.00,
        durationMinutes: 240,
        nextMaintenanceDate: 'En 3 meses',
      ),

      // Mantenimientos para Vehículo Toyota Hilux (asset_004)
      MaintenanceModel(
        id: 'maint_005',
        assetId: 'asset_004',
        assetName: 'Vehículo Toyota Hilux',
        title: 'Cambio de aceite y filtros',
        description: 'Cambio de aceite de motor, filtro de aceite y filtro de aire.',
        type: 'Preventivo',
        status: 'Completado',
        priority: 'Media',
        scheduledDate: now.subtract(const Duration(days: 45)),
        startedAt: now.subtract(const Duration(days: 45)),
        completedAt: now.subtract(const Duration(days: 45)),
        assignedTo: 'Taller Mecánico',
        performedBy: 'José Mecánico',
        notes: 'Cambio de aceite realizado. Vehículo en excelente estado.',
        tasksCompleted: [
          'Drenado de aceite usado',
          'Cambio de filtro de aceite',
          'Cambio de filtro de aire',
          'Llenado con aceite nuevo 15W-40',
          'Verificación de niveles'
        ],
        partsUsed: ['Aceite 15W-40 (5L)', 'Filtro de aceite', 'Filtro de aire'],
        cost: 85.00,
        durationMinutes: 45,
        nextMaintenanceDate: 'En 5000 km',
      ),

      MaintenanceModel(
        id: 'maint_006',
        assetId: 'asset_004',
        assetName: 'Vehículo Toyota Hilux',
        title: 'Revisión de frenos',
        description: 'Inspección y mantenimiento del sistema de frenos.',
        type: 'Preventivo',
        status: 'Programado',
        priority: 'Alta',
        scheduledDate: now.add(const Duration(days: 3)),
        assignedTo: 'Taller Mecánico',
        notes: 'Programado para revisión completa del sistema de frenos.',
        tasksCompleted: [],
        partsUsed: [],
      ),

      // Mantenimientos para Servidor Principal (asset_005)
      MaintenanceModel(
        id: 'maint_007',
        assetId: 'asset_005',
        assetName: 'Servidor Principal',
        title: 'Actualización de seguridad',
        description: 'Instalación de parches de seguridad y actualización del sistema.',
        type: 'Preventivo',
        status: 'Completado',
        priority: 'Crítica',
        scheduledDate: now.subtract(const Duration(days: 10)),
        startedAt: now.subtract(const Duration(days: 10)),
        completedAt: now.subtract(const Duration(days: 10)),
        assignedTo: 'Roberto Seguridad',
        performedBy: 'Roberto Seguridad',
        notes: 'Actualizaciones de seguridad instaladas exitosamente.',
        tasksCompleted: [
          'Instalación de parches Windows Server',
          'Actualización de antivirus corporativo',
          'Verificación de logs de seguridad',
          'Backup de configuraciones',
          'Reinicio programado'
        ],
        partsUsed: [],
        cost: 0.00,
        durationMinutes: 120,
        nextMaintenanceDate: 'En 1 mes',
      ),
    ];
  }
}
