class ReportModel {
  final String id;
  final String assetId;
  final String assetName;
  final String title;
  final String description;
  final String priority;
  final String status;
  final String type;
  final List<String> imageUrls;
  final String? location;
  final DateTime reportedAt;
  final String reportedBy;
  final String? assignedTo;
  final DateTime? assignedAt;
  final DateTime? resolvedAt;
  final String? resolution;
  final Map<String, dynamic>? metadata;

  const ReportModel({
    required this.id,
    required this.assetId,
    required this.assetName,
    required this.title,
    required this.description,
    required this.priority,
    required this.status,
    required this.type,
    this.imageUrls = const [],
    this.location,
    required this.reportedAt,
    required this.reportedBy,
    this.assignedTo,
    this.assignedAt,
    this.resolvedAt,
    this.resolution,
    this.metadata,
  });

  factory ReportModel.fromJson(Map<String, dynamic> json) {
    return ReportModel(
      id: json['id'] as String,
      assetId: json['asset_id'] as String,
      assetName: json['asset_name'] as String? ?? '',
      title: json['title'] as String,
      description: json['description'] as String,
      priority: json['priority'] as String,
      status: json['status'] as String,
      type: json['type'] as String? ?? 'Falla',
      imageUrls: json['image_urls'] != null
          ? List<String>.from(json['image_urls'] as List)
          : [],
      location: json['location'] as String?,
      reportedAt: DateTime.parse(json['reported_at'] as String),
      reportedBy: json['reported_by'] as String,
      assignedTo: json['assigned_to'] as String?,
      assignedAt: json['assigned_at'] != null
          ? DateTime.parse(json['assigned_at'] as String)
          : null,
      resolvedAt: json['resolved_at'] != null
          ? DateTime.parse(json['resolved_at'] as String)
          : null,
      resolution: json['resolution'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'asset_id': assetId,
      'asset_name': assetName,
      'title': title,
      'description': description,
      'priority': priority,
      'status': status,
      'type': type,
      'image_urls': imageUrls,
      'location': location,
      'reported_at': reportedAt.toIso8601String(),
      'reported_by': reportedBy,
      'assigned_to': assignedTo,
      'assigned_at': assignedAt?.toIso8601String(),
      'resolved_at': resolvedAt?.toIso8601String(),
      'resolution': resolution,
      'metadata': metadata,
    };
  }

  ReportModel copyWith({
    String? id,
    String? assetId,
    String? assetName,
    String? title,
    String? description,
    String? priority,
    String? status,
    String? type,
    List<String>? imageUrls,
    String? location,
    DateTime? reportedAt,
    String? reportedBy,
    String? assignedTo,
    DateTime? assignedAt,
    DateTime? resolvedAt,
    String? resolution,
    Map<String, dynamic>? metadata,
  }) {
    return ReportModel(
      id: id ?? this.id,
      assetId: assetId ?? this.assetId,
      assetName: assetName ?? this.assetName,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      type: type ?? this.type,
      imageUrls: imageUrls ?? this.imageUrls,
      location: location ?? this.location,
      reportedAt: reportedAt ?? this.reportedAt,
      reportedBy: reportedBy ?? this.reportedBy,
      assignedTo: assignedTo ?? this.assignedTo,
      assignedAt: assignedAt ?? this.assignedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      resolution: resolution ?? this.resolution,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'ReportModel(id: $id, title: $title, priority: $priority, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReportModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
