import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/services/image_service.dart';
import '../../../data/models/report_model.dart';
import '../../providers/report_provider.dart';
import '../../providers/asset_provider.dart';

// Clase para manejar cada falla individual
class FaultItem {
  String title;
  String description;
  String? selectedSystem;
  List<File> images;

  FaultItem({
    this.title = '',
    this.description = '',
    this.selectedSystem,
    List<File>? images,
  }) : images = images ?? [];
}

class ReportFormScreen extends StatefulWidget {
  final ReportModel? report; // Para editar un reporte existente

  const ReportFormScreen({super.key, this.report});

  @override
  State<ReportFormScreen> createState() => _ReportFormScreenState();
}

class _ReportFormScreenState extends State<ReportFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _reportedByController = TextEditingController();

  String? _selectedAssetId;
  String? _selectedAssetName;
  String _selectedPriority = 'Media';
  String _selectedType = 'Falla';
  String _selectedStatus = 'Abierto';
  String? _selectedAssignedTo;
  List<String> _imageUrls = [];
  List<File> _selectedImages = []; // Archivos de imagen locales
  bool _isLoading = false;
  bool _isLoadingImage = false; // Estado de carga de imagen

  // Variables para clasificación jerárquica
  String? _selectedCategory; // Vehículos, Equipo, Herramienta, Infraestructura
  String? _selectedVehicleType; // Unidades, Camionetas
  String? _selectedUnit; // Horus, Ji, Thot, Alfa, Seth, Rho
  final _providerController =
      TextEditingController(); // Para proveedor de camionetas
  final _plateController = TextEditingController(); // Para placa de camionetas

  // Variables adicionales
  String? _unitPlate; // Placa automática de la unidad
  final _horometerController = TextEditingController(); // Horómetro
  final _kilometrageController = TextEditingController(); // Kilometraje
  final _equipmentCodeController = TextEditingController(); // Código de equipo
  final _toolCodeController = TextEditingController(); // Código de herramienta
  final _infrastructureNameController =
      TextEditingController(); // Nombre de infraestructura
  String? _equipmentName; // Nombre automático del equipo
  String? _toolName; // Nombre automático de la herramienta
  bool _needsHorometer = false; // Si el equipo necesita horómetro

  // Variables para múltiples fallas
  List<FaultItem> _faults = [FaultItem()]; // Lista de fallas (mínimo 1)

  // Sistemas disponibles
  final List<String> _availableSystems = [
    'Cabina y Accesorios',
    'Slickline / Wireline',
    'Sistema de Suspensión',
    'Sistema Eléctrico',
    'Grúa',
    'Motor',
    'Sistema Hidráulico',
    'Sistema de Dirección',
    'Sistema de Frenos',
    'Torre',
    'Transmisión y Potencia',
    'Sistema de Medición',
  ];

  @override
  void initState() {
    super.initState();
    _initializeForm();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AssetProvider>(context, listen: false).loadAssets();
    });
  }

  void _initializeForm() {
    if (widget.report != null) {
      // Modo edición
      final report = widget.report!;
      _titleController.text = report.title;
      _descriptionController.text = report.description;
      _locationController.text = report.location ?? '';
      _reportedByController.text = report.reportedBy;
      _selectedAssetId = report.assetId;
      _selectedAssetName = report.assetName;
      _selectedPriority = report.priority;
      _selectedType = report.type;
      _selectedStatus = report.status;
      _selectedAssignedTo = report.assignedTo;
      _imageUrls = List.from(report.imageUrls);

      // Cargar imágenes existentes como archivos
      _selectedImages = report.imageUrls.map((path) => File(path)).toList();
    } else {
      // Modo creación - valores por defecto
      _reportedByController.text =
          'Usuario Actual'; // TODO: Obtener del AuthProvider
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _reportedByController.dispose();
    _providerController.dispose();
    _plateController.dispose();
    _horometerController.dispose();
    _kilometrageController.dispose();
    _equipmentCodeController.dispose();
    _toolCodeController.dispose();
    _infrastructureNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.report != null;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          isEditing
              ? 'Editar Reporte'
              : 'Nuevo Reporte de Falla o Solicitud de Trabajo',
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (isEditing)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _showDeleteDialog,
            ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'Guardando reporte...')
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Clasificación
                    _buildSectionTitle('Clasificación'),
                    const SizedBox(height: 16),

                    // Categoría principal
                    _buildCategorySelector(),
                    const SizedBox(height: 16),

                    // Clasificación jerárquica según categoría
                    if (_selectedCategory != null) ...[
                      _buildHierarchicalClassification(),
                      const SizedBox(height: 16),
                    ],

                    // Tipo
                    _buildDropdown(
                      label: 'Tipo',
                      value: _selectedType,
                      items: const ['Falla', 'Solicitud de Trabajo'],
                      icon: Icons.category,
                      onChanged: (value) {
                        setState(() {
                          _selectedType = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // Prioridad
                    _buildDropdown(
                      label: 'Prioridad',
                      value: _selectedPriority,
                      items: AppConstants.reportPriorities,
                      icon: Icons.priority_high,
                      onChanged: (value) {
                        setState(() {
                          _selectedPriority = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 24),

                    // Múltiples Fallas/Solicitudes
                    _buildSectionTitle('Fallas y Solicitudes (Máximo 10)'),
                    const SizedBox(height: 16),
                    _buildFaultsSection(),
                    const SizedBox(height: 24),

                    // Información básica (al final)
                    _buildSectionTitle('Información Básica'),
                    const SizedBox(height: 16),

                    // Ubicación
                    _buildTextField(
                      controller: _locationController,
                      label: 'Ubicación',
                      hint: 'Ej: Oficina Principal - Piso 2',
                      icon: Icons.location_on,
                      validator: (value) {
                        if (value?.isEmpty ?? true) {
                          return 'La ubicación es obligatoria';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Reportado por (automático, no editable)
                    _buildReadOnlyField(
                      label: 'Reportado por',
                      value:
                          'Juan Carlos Pérez', // TODO: Obtener del AuthProvider
                      icon: Icons.person,
                    ),
                    const SizedBox(height: 16),

                    // Cargo (automático, no editable)
                    _buildReadOnlyField(
                      label: 'Cargo',
                      value:
                          'Supervisor de Mantenimiento', // TODO: Obtener del AuthProvider
                      icon: Icons.work,
                    ),
                    const SizedBox(height: 16),

                    // Estado y Asignado a (solo en modo edición)
                    if (isEditing) ...[
                      // Estado
                      _buildDropdown(
                        label: 'Estado',
                        value: _selectedStatus,
                        items: AppConstants.reportStatuses,
                        icon: Icons.flag,
                        onChanged: (value) {
                          setState(() {
                            _selectedStatus = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Asignado a
                      _buildTextField(
                        controller: TextEditingController(
                          text: _selectedAssignedTo ?? '',
                        ),
                        label: 'Asignado a',
                        hint: 'Nombre del responsable',
                        icon: Icons.assignment_ind,
                        onChanged: (value) {
                          _selectedAssignedTo = value.isEmpty ? null : value;
                        },
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Botones de acción
                    _buildActionButtons(isEditing),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      validator: validator,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: AppColors.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
    );
  }

  Widget _buildDropdown({
    required String label,
    required String value,
    required List<String> items,
    required IconData icon,
    required void Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      isExpanded: true, // Esto evita el overflow
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppColors.primary),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 16,
        ), // Reduce padding
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: items.map((item) {
        return DropdownMenuItem(
          value: item,
          child: Text(
            item,
            overflow: TextOverflow.ellipsis, // Maneja texto largo
            style: const TextStyle(fontSize: 14), // Reduce tamaño de fuente
          ),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  Widget _buildAssetSelector() {
    return Consumer<AssetProvider>(
      builder: (context, assetProvider, _) {
        if (assetProvider.isLoading) {
          return const SizedBox(
            height: 60,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final assets = assetProvider.assets;

        return DropdownButtonFormField<String>(
          value: _selectedAssetId,
          isExpanded: true, // Evita overflow
          decoration: InputDecoration(
            labelText: 'Activo',
            hintText: 'Selecciona el activo afectado',
            prefixIcon: const Icon(Icons.devices, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Debes seleccionar un activo';
            }
            return null;
          },
          items: assets.map((asset) {
            return DropdownMenuItem(
              value: asset.id,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    asset.name,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    '${asset.type} - ${asset.location}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedAssetId = value;
              final selectedAsset = assets.firstWhere(
                (asset) => asset.id == value,
              );
              _selectedAssetName = selectedAsset.name;
            });
          },
        );
      },
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Botones para agregar imágenes
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _isLoadingImage ? null : _addImage,
                icon: _isLoadingImage
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.add_a_photo),
                label: Text(
                  _isLoadingImage ? 'Procesando...' : 'Agregar Imagen',
                ),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: const BorderSide(color: AppColors.primary),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _addMultipleImages,
                icon: const Icon(Icons.photo_library),
                label: const Text('Múltiples'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.secondary,
                  side: const BorderSide(color: AppColors.secondary),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),

        // Lista de imágenes seleccionadas
        if (_selectedImages.isNotEmpty) ...[
          const SizedBox(height: 16),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return _buildImagePreview(_selectedImages[index], index);
              },
            ),
          ),
        ],

        // Información sobre imágenes
        if (_selectedImages.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            '${_selectedImages.length} imagen(es) seleccionada(s)',
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildImagePreview(File imageFile, int index) {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      child: Stack(
        children: [
          // Imagen
          GestureDetector(
            onTap: () => _viewImage(imageFile),
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.border),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.file(imageFile, fit: BoxFit.cover),
              ),
            ),
          ),

          // Botón eliminar
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => _removeImage(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: AppColors.error,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(bool isEditing) {
    return Column(
      children: [
        // Botón principal
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton.icon(
            onPressed: _saveReport,
            icon: Icon(isEditing ? Icons.save : Icons.add),
            label: Text(
              isEditing ? 'Guardar Cambios' : 'Crear Reporte',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Botón cancelar
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton.icon(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.cancel),
            label: const Text(
              'Cancelar',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textSecondary,
              side: const BorderSide(color: AppColors.border),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _saveReport() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );

      // Convertir rutas de archivos locales a strings
      final List<String> imagePaths = _selectedImages
          .map((file) => file.path)
          .toList();
      print(
        '💾 Guardando reporte con ${imagePaths.length} imágenes: $imagePaths',
      );

      final report = ReportModel(
        id: widget.report?.id ?? '',
        assetId: _selectedAssetId!,
        assetName: _selectedAssetName!,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: _selectedPriority,
        status: _selectedStatus,
        type: _selectedType,
        imageUrls: imagePaths, // Usar las rutas de las imágenes seleccionadas
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        reportedAt: widget.report?.reportedAt ?? DateTime.now(),
        reportedBy: _reportedByController.text.trim(),
        assignedTo: _selectedAssignedTo,
        assignedAt: widget.report?.assignedAt,
        resolvedAt: widget.report?.resolvedAt,
        resolution: widget.report?.resolution,
        metadata: widget.report?.metadata ?? {},
      );

      bool success;
      if (widget.report != null) {
        // Actualizar reporte existente
        success = await reportProvider.updateReport(report);
      } else {
        // Crear nuevo reporte
        success = await reportProvider.createReport(report);
      }

      setState(() {
        _isLoading = false;
      });

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.report != null
                    ? 'Reporte actualizado exitosamente'
                    : 'Reporte creado exitosamente',
              ),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Error al guardar el reporte'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Reporte'),
        content: const Text(
          '¿Estás seguro de que quieres eliminar este reporte? Esta acción no se puede deshacer.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteReport();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text(
              'Eliminar',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteReport() async {
    if (widget.report == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );
      final success = await reportProvider.deleteReport(widget.report!.id);

      setState(() {
        _isLoading = false;
      });

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reporte eliminado exitosamente'),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Error al eliminar el reporte'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // Métodos para manejar imágenes
  Future<void> _addImage() async {
    setState(() {
      _isLoadingImage = true;
    });

    try {
      print('🔍 Iniciando selección de imagen...');
      final File? imageFile = await ImageService.pickImage(context);
      print('📸 Imagen seleccionada: ${imageFile?.path}');

      if (imageFile != null) {
        print('✅ Agregando imagen a la lista...');
        setState(() {
          _selectedImages.add(imageFile);
          _isLoadingImage = false;
        });
        print('📋 Total de imágenes: ${_selectedImages.length}');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Imagen agregada exitosamente'),
              backgroundColor: AppColors.success,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        print('❌ No se seleccionó ninguna imagen');
        setState(() {
          _isLoadingImage = false;
        });
      }
    } catch (e) {
      print('💥 Error al seleccionar imagen: $e');
      setState(() {
        _isLoadingImage = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al seleccionar imagen: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<void> _addMultipleImages() async {
    try {
      final List<File> imageFiles = await ImageService.pickMultipleImages();
      if (imageFiles.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(imageFiles);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al seleccionar imágenes: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _viewImage(File imageFile) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _ImageViewerScreen(imageFile: imageFile),
      ),
    );
  }

  // Sección de múltiples fallas
  Widget _buildFaultsSection() {
    return Column(
      children: [
        // Lista de fallas
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _faults.length,
          itemBuilder: (context, index) {
            return _buildFaultCard(index);
          },
        ),

        // Botón para agregar nueva falla (máximo 10)
        if (_faults.length < 10) ...[
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _addNewFault,
              icon: const Icon(Icons.add),
              label: Text('Agregar ${_selectedType} (${_faults.length}/10)'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: const BorderSide(color: AppColors.primary),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  // Tarjeta individual de falla
  Widget _buildFaultCard(int index) {
    final fault = _faults[index];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Encabezado de la falla
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${_selectedType} ${index + 1}',
                    style: const TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                if (_faults.length > 1)
                  IconButton(
                    onPressed: () => _removeFault(index),
                    icon: const Icon(
                      Icons.delete_outline,
                      color: AppColors.error,
                    ),
                    tooltip: 'Eliminar ${_selectedType.toLowerCase()}',
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Título de la falla
            TextFormField(
              initialValue: fault.title,
              decoration: InputDecoration(
                labelText: 'Título de la ${_selectedType}',
                hintText: 'Ej: Pantalla no enciende',
                prefixIcon: const Icon(Icons.title, color: AppColors.primary),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.border),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: AppColors.primary,
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'El título es obligatorio';
                }
                if (value!.length < 5) {
                  return 'El título debe tener al menos 5 caracteres';
                }
                return null;
              },
              onChanged: (value) {
                fault.title = value;
              },
            ),
            const SizedBox(height: 16),

            // Sistema (debajo del título)
            _buildDropdown(
              label: 'Sistema',
              value: fault.selectedSystem ?? '',
              items: ['', ..._availableSystems],
              icon: Icons.settings,
              onChanged: (value) {
                setState(() {
                  fault.selectedSystem = value?.isEmpty == true ? null : value;
                });
              },
            ),
            const SizedBox(height: 16),

            // Descripción de la falla
            TextFormField(
              initialValue: fault.description,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'Descripción de la ${_selectedType}',
                hintText: 'Describe detalladamente el problema...',
                prefixIcon: const Icon(
                  Icons.description,
                  color: AppColors.primary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.border),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: AppColors.primary,
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'La descripción es obligatoria';
                }
                if (value!.length < 10) {
                  return 'La descripción debe tener al menos 10 caracteres';
                }
                return null;
              },
              onChanged: (value) {
                fault.description = value;
              },
            ),
            const SizedBox(height: 16),

            // Imágenes de la falla (máximo 2)
            _buildFaultImageSection(index, fault),
          ],
        ),
      ),
    );
  }

  // Sección de imágenes para cada falla
  Widget _buildFaultImageSection(int faultIndex, FaultItem fault) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.photo_camera, color: AppColors.primary, size: 20),
            const SizedBox(width: 8),
            Text(
              'Imágenes (${fault.images.length}/2)',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Botones para agregar imágenes
        if (fault.images.length < 2) ...[
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _addImageToFault(faultIndex),
                  icon: const Icon(Icons.add_a_photo),
                  label: const Text('Agregar Imagen'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.secondary,
                    side: const BorderSide(color: AppColors.secondary),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
        ],

        // Lista de imágenes
        if (fault.images.isNotEmpty) ...[
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: fault.images.length,
              itemBuilder: (context, imageIndex) {
                return _buildFaultImagePreview(
                  fault.images[imageIndex],
                  faultIndex,
                  imageIndex,
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  // Preview de imagen para falla específica
  Widget _buildFaultImagePreview(
    File imageFile,
    int faultIndex,
    int imageIndex,
  ) {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      child: Stack(
        children: [
          // Imagen
          GestureDetector(
            onTap: () => _viewImage(imageFile),
            child: Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.border),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.file(imageFile, fit: BoxFit.cover),
              ),
            ),
          ),

          // Botón eliminar
          Positioned(
            top: 2,
            right: 2,
            child: GestureDetector(
              onTap: () => _removeImageFromFault(faultIndex, imageIndex),
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: const BoxDecoration(
                  color: AppColors.error,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Selector de categoría principal
  Widget _buildCategorySelector() {
    const categories = [
      'Vehículos',
      'Equipo',
      'Herramienta',
      'Infraestructura',
    ];

    return _buildDropdown(
      label: 'Categoría',
      value: _selectedCategory ?? '',
      items: ['', ...categories],
      icon: Icons.category_outlined,
      onChanged: (value) {
        setState(() {
          _selectedCategory = value?.isEmpty == true ? null : value;
          // Limpiar selecciones dependientes
          _selectedVehicleType = null;
          _selectedUnit = null;
          _providerController.clear();
          _plateController.clear();
        });
      },
    );
  }

  // Clasificación jerárquica según la categoría seleccionada
  Widget _buildHierarchicalClassification() {
    if (_selectedCategory == null) return const SizedBox.shrink();

    switch (_selectedCategory) {
      case 'Vehículos':
        return _buildVehicleClassification();
      case 'Equipo':
        return _buildEquipmentClassification();
      case 'Herramienta':
        return _buildToolClassification();
      case 'Infraestructura':
        return _buildInfrastructureClassification();
      default:
        return const SizedBox.shrink();
    }
  }

  // Clasificación específica para vehículos
  Widget _buildVehicleClassification() {
    return Column(
      children: [
        // Tipo de vehículo
        _buildDropdown(
          label: 'Tipo de Vehículo',
          value: _selectedVehicleType ?? '',
          items: const ['', 'Unidades', 'Camionetas'],
          icon: Icons.directions_car,
          onChanged: (value) {
            setState(() {
              _selectedVehicleType = value?.isEmpty == true ? null : value;
              // Limpiar selecciones dependientes
              _selectedUnit = null;
              _unitPlate = null;
              _providerController.clear();
              _plateController.clear();
              _horometerController.clear();
              _kilometrageController.clear();
            });
          },
        ),
        const SizedBox(height: 16),

        // Clasificación según tipo de vehículo
        if (_selectedVehicleType == 'Unidades') ...[
          _buildDropdown(
            label: 'Seleccionar Unidad',
            value: _selectedUnit ?? '',
            items: const [
              '',
              'Unidad Horus',
              'Unidad Ji',
              'Unidad Thot',
              'Unidad Alfa',
              'Unidad Seth',
              'Unidad Rho',
            ],
            icon: Icons.local_shipping,
            onChanged: (value) {
              setState(() {
                _selectedUnit = value?.isEmpty == true ? null : value;
                // Asignar placa automáticamente según la unidad
                _unitPlate = _getUnitPlate(_selectedUnit);
              });
            },
          ),
          const SizedBox(height: 16),

          // Mostrar placa de la unidad (solo lectura)
          if (_selectedUnit != null && _unitPlate != null) ...[
            _buildReadOnlyField(
              label: 'Placa de la Unidad',
              value: _unitPlate!,
              icon: Icons.confirmation_number,
            ),
            const SizedBox(height: 16),

            // Horómetro
            _buildTextField(
              controller: _horometerController,
              label: 'Horómetro (horas)',
              hint: 'Ej: 1250.5',
              icon: Icons.schedule,
              validator: (value) {
                if (value?.isNotEmpty == true) {
                  final hours = double.tryParse(value!);
                  if (hours == null || hours < 0) {
                    return 'Ingrese un número válido de horas';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Kilometraje
            _buildTextField(
              controller: _kilometrageController,
              label: 'Kilometraje (km)',
              hint: 'Ej: 85420',
              icon: Icons.speed,
              validator: (value) {
                if (value?.isNotEmpty == true) {
                  final km = double.tryParse(value!);
                  if (km == null || km < 0) {
                    return 'Ingrese un número válido de kilómetros';
                  }
                }
                return null;
              },
            ),
          ],
        ] else if (_selectedVehicleType == 'Camionetas') ...[
          _buildTextField(
            controller: _providerController,
            label: 'Nombre del Proveedor',
            hint: 'Ej: Transportes ABC',
            icon: Icons.business,
            validator: (value) {
              if (_selectedVehicleType == 'Camionetas' &&
                  (value?.isEmpty ?? true)) {
                return 'El nombre del proveedor es obligatorio';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _plateController,
            label: 'Placa de la Camioneta',
            hint: 'Ej: ABC-123',
            icon: Icons.confirmation_number,
            validator: (value) {
              if (_selectedVehicleType == 'Camionetas' &&
                  (value?.isEmpty ?? true)) {
                return 'La placa es obligatoria';
              }
              return null;
            },
          ),
        ],
      ],
    );
  }

  // Mensaje simple para otras categorías
  Widget _buildSimpleMessage(String category) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(_getCategoryIcon(category), color: AppColors.primary, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Categoría seleccionada: $category',
              style: const TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Clasificación específica para equipos
  Widget _buildEquipmentClassification() {
    return Column(
      children: [
        _buildTextField(
          controller: _equipmentCodeController,
          label: 'Código del Equipo',
          hint: 'Ej: EQ-001',
          icon: Icons.qr_code,
          onChanged: (value) {
            setState(() {
              _equipmentName = _getEquipmentName(value);
              _needsHorometer = _equipmentNeedsHorometer(value);
            });
          },
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'El código del equipo es obligatorio';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Mostrar nombre del equipo automáticamente
        if (_equipmentName != null) ...[
          _buildReadOnlyField(
            label: 'Nombre del Equipo',
            value: _equipmentName!,
            icon: Icons.precision_manufacturing,
          ),
          const SizedBox(height: 16),

          // Horómetro si el equipo lo necesita
          if (_needsHorometer) ...[
            _buildTextField(
              controller: _horometerController,
              label: 'Horómetro (horas)',
              hint: 'Ej: 1250.5',
              icon: Icons.schedule,
              validator: (value) {
                // El horómetro es opcional para todos los equipos
                if (value?.isNotEmpty == true) {
                  final hours = double.tryParse(value!);
                  if (hours == null || hours < 0) {
                    return 'Ingrese un número válido de horas';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
          ],
        ],
      ],
    );
  }

  // Clasificación específica para herramientas
  Widget _buildToolClassification() {
    return Column(
      children: [
        _buildTextField(
          controller: _toolCodeController,
          label: 'Código de la Herramienta',
          hint: 'Ej: HE-001',
          icon: Icons.qr_code,
          onChanged: (value) {
            setState(() {
              _toolName = _getToolName(value);
            });
          },
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'El código de la herramienta es obligatorio';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Mostrar nombre de la herramienta automáticamente
        if (_toolName != null) ...[
          _buildReadOnlyField(
            label: 'Nombre de la Herramienta',
            value: _toolName!,
            icon: Icons.build,
          ),
        ],
      ],
    );
  }

  // Clasificación específica para infraestructura
  Widget _buildInfrastructureClassification() {
    return Column(
      children: [
        _buildTextField(
          controller: _infrastructureNameController,
          label: 'Nombre de la Infraestructura',
          hint: 'Ej: Edificio Principal, Sala de Servidores',
          icon: Icons.domain,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'El nombre de la infraestructura es obligatorio';
            }
            return null;
          },
        ),
      ],
    );
  }

  // Campo de solo lectura
  Widget _buildReadOnlyField({
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              Icon(icon, color: Colors.grey[600], size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Obtener placa según la unidad seleccionada
  String? _getUnitPlate(String? unit) {
    if (unit == null) return null;

    switch (unit) {
      case 'Unidad Horus':
        return 'HOR-001';
      case 'Unidad Ji':
        return 'JI-002';
      case 'Unidad Thot':
        return 'THO-003';
      case 'Unidad Alfa':
        return 'ALF-004';
      case 'Unidad Seth':
        return 'SET-005';
      case 'Unidad Rho':
        return 'RHO-006';
      default:
        return null;
    }
  }

  // Obtener nombre del equipo según código
  String? _getEquipmentName(String code) {
    if (code.isEmpty) return null;

    // Simulación de base de datos de equipos
    final equipmentDatabase = {
      'EQ-001': 'Compresor de Aire Principal',
      'EQ-002': 'Generador Eléctrico 500KW',
      'EQ-003': 'Bomba Centrífuga Industrial',
      'EQ-004': 'Soldadora MIG/MAG',
      'EQ-005': 'Torno CNC Haas',
      'EQ-006': 'Fresadora Universal',
      'EQ-007': 'Grúa Puente 10 Ton',
    };

    return equipmentDatabase[code.toUpperCase()];
  }

  // Verificar si el equipo necesita horómetro
  bool _equipmentNeedsHorometer(String code) {
    if (code.isEmpty) return false;

    // Equipos que requieren horómetro
    final horometerRequired = {
      'EQ-001', // Compresor
      'EQ-002', // Generador
      'EQ-003', // Bomba
      'EQ-005', // Torno CNC
      'EQ-006', // Fresadora
      'EQ-007', // Grúa
    };

    return horometerRequired.contains(code.toUpperCase());
  }

  // Obtener nombre de la herramienta según código
  String? _getToolName(String code) {
    if (code.isEmpty) return null;

    // Simulación de base de datos de herramientas
    final toolDatabase = {
      'HE-001': 'Taladro Percutor Bosch',
      'HE-002': 'Amoladora Angular 9"',
      'HE-003': 'Martillo Neumático',
      'HE-004': 'Llave de Impacto 1/2"',
      'HE-005': 'Sierra Circular 7 1/4"',
      'HE-006': 'Multímetro Digital Fluke',
      'HE-007': 'Nivel Láser Rotativo',
    };

    return toolDatabase[code.toUpperCase()];
  }

  // Obtener icono según categoría
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Vehículos':
        return Icons.directions_car;
      case 'Equipo':
        return Icons.precision_manufacturing;
      case 'Herramienta':
        return Icons.build;
      case 'Infraestructura':
        return Icons.domain;
      default:
        return Icons.category;
    }
  }

  // Métodos para manejar múltiples fallas
  void _addNewFault() {
    if (_faults.length < 10) {
      setState(() {
        _faults.add(FaultItem());
      });
    }
  }

  void _removeFault(int index) {
    if (_faults.length > 1) {
      setState(() {
        _faults.removeAt(index);
      });
    }
  }

  // Agregar imagen a una falla específica
  Future<void> _addImageToFault(int faultIndex) async {
    if (_faults[faultIndex].images.length >= 2) return;

    try {
      final File? imageFile = await ImageService.pickImage(context);

      if (imageFile != null) {
        setState(() {
          _faults[faultIndex].images.add(imageFile);
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Imagen agregada a ${_selectedType} ${faultIndex + 1}',
              ),
              backgroundColor: AppColors.success,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al agregar imagen: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // Eliminar imagen de una falla específica
  void _removeImageFromFault(int faultIndex, int imageIndex) {
    setState(() {
      _faults[faultIndex].images.removeAt(imageIndex);
    });
  }
}

// Pantalla para ver imagen en pantalla completa
class _ImageViewerScreen extends StatelessWidget {
  final File imageFile;

  const _ImageViewerScreen({required this.imageFile});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: const Text('Vista de Imagen'),
      ),
      body: Center(child: InteractiveViewer(child: Image.file(imageFile))),
    );
  }
}
