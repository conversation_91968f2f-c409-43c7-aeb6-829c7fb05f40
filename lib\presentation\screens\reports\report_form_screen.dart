import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../data/models/report_model.dart';
import '../../providers/report_provider.dart';
import '../../providers/asset_provider.dart';

class ReportFormScreen extends StatefulWidget {
  final ReportModel? report; // Para editar un reporte existente

  const ReportFormScreen({super.key, this.report});

  @override
  State<ReportFormScreen> createState() => _ReportFormScreenState();
}

class _ReportFormScreenState extends State<ReportFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _reportedByController = TextEditingController();

  String? _selectedAssetId;
  String? _selectedAssetName;
  String _selectedPriority = 'Media';
  String _selectedType = 'Falla';
  String _selectedStatus = 'Abierto';
  String? _selectedAssignedTo;
  List<String> _imageUrls = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AssetProvider>(context, listen: false).loadAssets();
    });
  }

  void _initializeForm() {
    if (widget.report != null) {
      // Modo edición
      final report = widget.report!;
      _titleController.text = report.title;
      _descriptionController.text = report.description;
      _locationController.text = report.location ?? '';
      _reportedByController.text = report.reportedBy;
      _selectedAssetId = report.assetId;
      _selectedAssetName = report.assetName;
      _selectedPriority = report.priority;
      _selectedType = report.type;
      _selectedStatus = report.status;
      _selectedAssignedTo = report.assignedTo;
      _imageUrls = List.from(report.imageUrls);
    } else {
      // Modo creación - valores por defecto
      _reportedByController.text =
          'Usuario Actual'; // TODO: Obtener del AuthProvider
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _reportedByController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.report != null;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(isEditing ? 'Editar Reporte' : 'Nuevo Reporte'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (isEditing)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _showDeleteDialog,
            ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'Guardando reporte...')
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Información básica
                    _buildSectionTitle('Información Básica'),
                    const SizedBox(height: 16),

                    // Título del reporte
                    _buildTextField(
                      controller: _titleController,
                      label: 'Título del Reporte',
                      hint: 'Ej: Pantalla no enciende',
                      icon: Icons.title,
                      validator: (value) {
                        if (value?.isEmpty ?? true) {
                          return 'El título es obligatorio';
                        }
                        if (value!.length < 5) {
                          return 'El título debe tener al menos 5 caracteres';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Descripción
                    _buildTextField(
                      controller: _descriptionController,
                      label: 'Descripción',
                      hint: 'Describe detalladamente el problema...',
                      icon: Icons.description,
                      maxLines: 4,
                      validator: (value) {
                        if (value?.isEmpty ?? true) {
                          return 'La descripción es obligatoria';
                        }
                        if (value!.length < 10) {
                          return 'La descripción debe tener al menos 10 caracteres';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),

                    // Clasificación
                    _buildSectionTitle('Clasificación'),
                    const SizedBox(height: 16),

                    // Activo
                    _buildAssetSelector(),
                    const SizedBox(height: 16),

                    // Tipo y Prioridad en fila
                    Row(
                      children: [
                        Expanded(
                          child: _buildDropdown(
                            label: 'Tipo',
                            value: _selectedType,
                            items: AppConstants.reportTypes,
                            icon: Icons.category,
                            onChanged: (value) {
                              setState(() {
                                _selectedType = value!;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildDropdown(
                            label: 'Prioridad',
                            value: _selectedPriority,
                            items: AppConstants.reportPriorities,
                            icon: Icons.priority_high,
                            onChanged: (value) {
                              setState(() {
                                _selectedPriority = value!;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Detalles adicionales
                    _buildSectionTitle('Detalles Adicionales'),
                    const SizedBox(height: 16),

                    // Ubicación
                    _buildTextField(
                      controller: _locationController,
                      label: 'Ubicación',
                      hint: 'Ej: Oficina Principal - Piso 2',
                      icon: Icons.location_on,
                    ),
                    const SizedBox(height: 16),

                    // Reportado por
                    _buildTextField(
                      controller: _reportedByController,
                      label: 'Reportado por',
                      hint: 'Nombre de quien reporta',
                      icon: Icons.person,
                      validator: (value) {
                        if (value?.isEmpty ?? true) {
                          return 'Este campo es obligatorio';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Estado y Asignado a (solo en modo edición)
                    if (isEditing) ...[
                      Row(
                        children: [
                          Expanded(
                            child: _buildDropdown(
                              label: 'Estado',
                              value: _selectedStatus,
                              items: AppConstants.reportStatuses,
                              icon: Icons.flag,
                              onChanged: (value) {
                                setState(() {
                                  _selectedStatus = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildTextField(
                              controller: TextEditingController(
                                text: _selectedAssignedTo ?? '',
                              ),
                              label: 'Asignado a',
                              hint: 'Nombre del responsable',
                              icon: Icons.assignment_ind,
                              onChanged: (value) {
                                _selectedAssignedTo = value.isEmpty
                                    ? null
                                    : value;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Imágenes (placeholder)
                    _buildSectionTitle('Imágenes'),
                    const SizedBox(height: 16),
                    _buildImageSection(),
                    const SizedBox(height: 32),

                    // Botones de acción
                    _buildActionButtons(isEditing),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      validator: validator,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: AppColors.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
    );
  }

  Widget _buildDropdown({
    required String label,
    required String value,
    required List<String> items,
    required IconData icon,
    required void Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppColors.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: items.map((item) {
        return DropdownMenuItem(value: item, child: Text(item));
      }).toList(),
      onChanged: onChanged,
    );
  }

  Widget _buildAssetSelector() {
    return Consumer<AssetProvider>(
      builder: (context, assetProvider, _) {
        if (assetProvider.isLoading) {
          return const SizedBox(
            height: 60,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final assets = assetProvider.assets;

        return DropdownButtonFormField<String>(
          value: _selectedAssetId,
          decoration: InputDecoration(
            labelText: 'Activo',
            hintText: 'Selecciona el activo afectado',
            prefixIcon: const Icon(Icons.devices, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Debes seleccionar un activo';
            }
            return null;
          },
          items: assets.map((asset) {
            return DropdownMenuItem(
              value: asset.id,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    asset.name,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    '${asset.type} - ${asset.location}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedAssetId = value;
              final selectedAsset = assets.firstWhere(
                (asset) => asset.id == value,
              );
              _selectedAssetName = selectedAsset.name;
            });
          },
        );
      },
    );
  }

  Widget _buildImageSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.border),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: Column(
        children: [
          Icon(Icons.camera_alt, size: 48, color: AppColors.textHint),
          const SizedBox(height: 8),
          const Text(
            'Agregar Imágenes',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'Próximamente: Captura o selecciona imágenes',
            style: TextStyle(fontSize: 12, color: AppColors.textHint),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(bool isEditing) {
    return Column(
      children: [
        // Botón principal
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton.icon(
            onPressed: _saveReport,
            icon: Icon(isEditing ? Icons.save : Icons.add),
            label: Text(
              isEditing ? 'Guardar Cambios' : 'Crear Reporte',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Botón cancelar
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton.icon(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.cancel),
            label: const Text(
              'Cancelar',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textSecondary,
              side: const BorderSide(color: AppColors.border),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _saveReport() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );

      final report = ReportModel(
        id: widget.report?.id ?? '',
        assetId: _selectedAssetId!,
        assetName: _selectedAssetName!,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: _selectedPriority,
        status: _selectedStatus,
        type: _selectedType,
        imageUrls: _imageUrls,
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        reportedAt: widget.report?.reportedAt ?? DateTime.now(),
        reportedBy: _reportedByController.text.trim(),
        assignedTo: _selectedAssignedTo,
        assignedAt: widget.report?.assignedAt,
        resolvedAt: widget.report?.resolvedAt,
        resolution: widget.report?.resolution,
        metadata: widget.report?.metadata ?? {},
      );

      bool success;
      if (widget.report != null) {
        // Actualizar reporte existente
        success = await reportProvider.updateReport(report);
      } else {
        // Crear nuevo reporte
        success = await reportProvider.createReport(report);
      }

      setState(() {
        _isLoading = false;
      });

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.report != null
                    ? 'Reporte actualizado exitosamente'
                    : 'Reporte creado exitosamente',
              ),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Error al guardar el reporte'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Reporte'),
        content: const Text(
          '¿Estás seguro de que quieres eliminar este reporte? Esta acción no se puede deshacer.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteReport();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text(
              'Eliminar',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteReport() async {
    if (widget.report == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );
      final success = await reportProvider.deleteReport(widget.report!.id);

      setState(() {
        _isLoading = false;
      });

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reporte eliminado exitosamente'),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Error al eliminar el reporte'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
