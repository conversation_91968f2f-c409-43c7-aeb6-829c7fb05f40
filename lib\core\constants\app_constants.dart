class AppConstants {
  // App Info
  static const String appName = 'MTTO 60';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Sistema de Mantenimiento de Activos';

  // API & Database - Appwrite
  static const String appwriteEndpoint = 'https://fra.cloud.appwrite.io/v1';
  static const String appwriteProjectId =
      '6830ac5d0014d3856d13'; // Reemplaza con tu Project ID
  static const String appwriteDatabaseId =
      'mtto_60_db'; // Lo crearemos en el siguiente paso

  // Collections IDs
  static const String usersCollectionId = 'users';
  static const String assetsCollectionId = 'assets';
  static const String reportsCollectionId = 'reports';
  static const String workOrdersCollectionId = 'work_orders';

  // Storage Bucket IDs
  static const String assetImagesBucketId = 'asset-images';
  static const String documentsBucketId = 'documents';

  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';

  // Asset Types
  static const List<String> assetTypes = [
    'Maquinaria',
    'Vehículo',
    'Equipo',
    'Herramienta',
    'Mobiliario',
    'Tecnología',
    'Infraestructura',
  ];

  // Asset Status
  static const List<String> assetStatuses = [
    'Operativo',
    'Mantenimiento',
    'En Reparación',
    'Fuera de Servicio',
    'En Reserva',
  ];

  // Report Types
  static const List<String> reportTypes = [
    'Falla',
    'Mantenimiento',
    'Inspección',
    'Mejora',
  ];

  // Report Priorities
  static const List<String> reportPriorities = [
    'Baja',
    'Media',
    'Alta',
    'Crítica',
  ];

  // Report Status
  static const List<String> reportStatuses = [
    'Abierto',
    'En Progreso',
    'En Revisión',
    'Resuelto',
    'Cerrado',
  ];

  // Priority Levels
  static const List<String> priorityLevels = [
    'Baja',
    'Media',
    'Alta',
    'Crítica',
  ];

  // Work Order Status
  static const List<String> workOrderStatus = [
    'Pendiente',
    'En Progreso',
    'Completada',
    'Cancelada',
  ];

  // User Roles
  static const List<String> userRoles = ['Admin', 'Técnico', 'Reportador'];

  // Maintenance Types
  static const List<String> maintenanceTypes = [
    'Preventivo',
    'Correctivo',
    'Predictivo',
    'Emergencia',
  ];
}
