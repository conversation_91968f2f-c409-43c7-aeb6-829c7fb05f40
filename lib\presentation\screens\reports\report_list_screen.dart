import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/app_utils.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../data/models/report_model.dart';
import '../../providers/report_provider.dart';
import 'report_detail_screen.dart';
import 'report_form_screen.dart';
import 'qr_scanner_screen.dart';

class ReportListScreen extends StatefulWidget {
  const ReportListScreen({super.key});

  @override
  State<ReportListScreen> createState() => _ReportListScreenState();
}

class _ReportListScreenState extends State<ReportListScreen> {
  final _searchController = TextEditingController();
  String _selectedTypeFilter = '';
  String _selectedStatusFilter = '';
  String _selectedPriorityFilter = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ReportProvider>(context, listen: false).loadReports();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(AppStrings.reports),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<ReportProvider>(context, listen: false).loadReports();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Barra de búsqueda
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.white,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Buscar reportes...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _updateSearch('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.border),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.primary),
                ),
              ),
              onChanged: _updateSearch,
            ),
          ),

          // Filtros activos
          if (_hasActiveFilters())
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              color: Colors.grey.shade100,
              child: Row(
                children: [
                  const Text(
                    'Filtros: ',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Expanded(
                    child: Wrap(
                      spacing: 8,
                      children: [
                        if (_selectedTypeFilter.isNotEmpty)
                          Chip(
                            label: Text(_selectedTypeFilter),
                            onDeleted: () => _clearTypeFilter(),
                            backgroundColor: AppColors.primary.withValues(
                              alpha: 0.1,
                            ),
                          ),
                        if (_selectedStatusFilter.isNotEmpty)
                          Chip(
                            label: Text(_selectedStatusFilter),
                            onDeleted: () => _clearStatusFilter(),
                            backgroundColor: AppColors.secondary.withValues(
                              alpha: 0.1,
                            ),
                          ),
                        if (_selectedPriorityFilter.isNotEmpty)
                          Chip(
                            label: Text(_selectedPriorityFilter),
                            onDeleted: () => _clearPriorityFilter(),
                            backgroundColor: AppColors.warning.withValues(
                              alpha: 0.1,
                            ),
                          ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: _clearAllFilters,
                    child: const Text('Limpiar todo'),
                  ),
                ],
              ),
            ),

          // Lista de reportes
          Expanded(
            child: Consumer<ReportProvider>(
              builder: (context, reportProvider, _) {
                if (reportProvider.isLoading) {
                  return const LoadingWidget(message: 'Cargando reportes...');
                }

                if (reportProvider.errorMessage != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppColors.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          reportProvider.errorMessage!,
                          style: const TextStyle(
                            fontSize: 16,
                            color: AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => reportProvider.loadReports(),
                          child: const Text('Reintentar'),
                        ),
                      ],
                    ),
                  );
                }

                final reports = reportProvider.filteredReports;

                if (reports.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.report_outlined,
                          size: 64,
                          color: AppColors.textHint,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'No hay reportes disponibles',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Crea tu primer reporte para comenzar',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textHint,
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: () => _navigateToReportForm(),
                          icon: const Icon(Icons.add),
                          label: const Text('Crear Reporte'),
                        ),
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => reportProvider.loadReports(),
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: reports.length,
                    itemBuilder: (context, index) {
                      final report = reports[index];
                      return _buildReportCard(report);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Botón de escanear QR
          FloatingActionButton(
            onPressed: _navigateToQrScanner,
            backgroundColor: AppColors.secondary,
            foregroundColor: Colors.white,
            heroTag: "qr_scanner",
            child: const Icon(Icons.qr_code_scanner),
          ),
          const SizedBox(height: 16),
          // Botón de crear reporte normal
          FloatingActionButton(
            onPressed: _navigateToReportForm,
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            heroTag: "create_report",
            child: const Icon(Icons.add),
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(ReportModel report) {
    final priorityColor = _getPriorityColor(report.priority);
    final textColor = _getTextColorForBackground(report.priority);
    final secondaryTextColor = _getSecondaryTextColorForBackground(
      report.priority,
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      color: priorityColor.withValues(
        alpha: 0.2,
      ), // Fondo con color de prioridad
      elevation: 3,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: priorityColor.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: InkWell(
          onTap: () => _navigateToReportDetail(report),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getPriorityColor(
                          report.priority,
                        ), // Fondo con color de prioridad
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getReportIcon(report.type),
                        color: _getIconColorForPriority(
                          report.priority,
                        ), // Color del icono adaptado
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            report.title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: textColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Activo: ${report.assetName}',
                            style: TextStyle(
                              fontSize: 14,
                              color: secondaryTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(report.status),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        report.status,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildPriorityChip(
                        label: report.priority,
                        color: _getPriorityColor(report.priority),
                        icon: _getPriorityIcon(report.priority),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildInfoChip(
                        icon: _getTypeChipIcon(
                          report.type,
                        ), // Usar icono específico
                        label: report.type,
                        color: Colors
                            .indigo
                            .shade700, // Color más oscuro y visible
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  report.description,
                  style: TextStyle(fontSize: 14, color: secondaryTextColor),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: secondaryTextColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      AppUtils.formatDate(report.reportedAt),
                      style: TextStyle(fontSize: 12, color: secondaryTextColor),
                    ),
                    const Spacer(),
                    if (report.assignedTo != null) ...[
                      Icon(Icons.person, size: 14, color: secondaryTextColor),
                      const SizedBox(width: 4),
                      Text(
                        report.assignedTo!,
                        style: TextStyle(
                          fontSize: 12,
                          color: secondaryTextColor,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(
          alpha: 0.9,
        ), // Fondo blanco semi-transparente
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w600, // Texto más grueso
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip({
    required String label,
    required Color color,
    IconData? icon,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(
          alpha: 0.9,
        ), // Fondo blanco semi-transparente
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 4),
          ],
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w600, // Texto más grueso
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getReportIcon(String type) {
    switch (type.toLowerCase()) {
      case 'falla':
        return Icons.error_outline; // Volver al icono original
      case 'mantenimiento':
        return Icons.build; // Mantener el icono de herramientas
      case 'inspección':
        return Icons.search; // Volver al icono original
      case 'mejora':
        return Icons.trending_up;
      default:
        return Icons.report_problem;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.black;
      case 'alta':
        return Colors.red.shade700; // Rojo más oscuro para mejor contraste
      case 'media':
        return Colors.blue.shade700; // Azul más oscuro para mejor contraste
      case 'baja':
        return Colors
            .amber
            .shade700; // Amarillo más oscuro para mejor contraste
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'abierto':
        return AppColors.info;
      case 'en progreso':
        return AppColors.warning;
      case 'en revisión':
        return Colors.purple;
      case 'resuelto':
        return AppColors.success;
      case 'cerrado':
        return Colors.green.shade900; // Verde muy oscuro para cerrado
      default:
        return AppColors.textSecondary;
    }
  }

  // Función para determinar el color de texto principal según el fondo
  Color _getTextColorForBackground(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.white; // Texto blanco para fondo negro
      case 'alta':
        return Colors.white; // Texto blanco para fondo rojo
      case 'media':
        return Colors.white; // Texto blanco para fondo azul
      case 'baja':
        return Colors.black87; // Texto negro para fondo amarillo
      default:
        return AppColors.textPrimary;
    }
  }

  // Función para determinar el color de texto secundario según el fondo
  Color _getSecondaryTextColorForBackground(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.white70; // Texto blanco semi-transparente
      case 'alta':
        return Colors.white70; // Texto blanco semi-transparente
      case 'media':
        return Colors.white70; // Texto blanco semi-transparente
      case 'baja':
        return Colors.black54; // Texto negro semi-transparente
      default:
        return AppColors.textSecondary;
    }
  }

  // Función para determinar el color del icono según la prioridad
  Color _getIconColorForPriority(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.white; // Icono blanco sobre fondo negro
      case 'alta':
        return Colors.white; // Icono blanco sobre fondo rojo
      case 'media':
        return Colors.white; // Icono blanco sobre fondo azul
      case 'baja':
        return Colors.black87; // Icono negro sobre fondo amarillo
      default:
        return AppColors.textPrimary;
    }
  }

  // Función para obtener el icono de prioridad
  IconData? _getPriorityIcon(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Icons.emergency; // Icono de urgente para crítico
      case 'alta':
        return Icons.dangerous; // Icono de peligro para alto
      case 'media':
        return Icons.warning; // Icono de advertencia para medio
      case 'baja':
        return null; // Sin icono para baja
      default:
        return Icons.priority_high;
    }
  }

  // Función para obtener el icono del chip de tipo
  IconData _getTypeChipIcon(String type) {
    switch (type.toLowerCase()) {
      case 'falla':
        return Icons.broken_image; // Icono de algo roto para fallas
      case 'mantenimiento':
        return Icons.build; // Icono de herramientas para mantenimiento
      case 'inspección':
        return Icons.back_hand; // Icono de mano para inspección
      case 'mejora':
        return Icons.trending_up;
      default:
        return Icons.category;
    }
  }

  void _updateSearch(String query) {
    final reportProvider = Provider.of<ReportProvider>(context, listen: false);
    reportProvider.searchReports(query);
  }

  bool _hasActiveFilters() {
    return _selectedTypeFilter.isNotEmpty ||
        _selectedStatusFilter.isNotEmpty ||
        _selectedPriorityFilter.isNotEmpty;
  }

  void _clearTypeFilter() {
    setState(() {
      _selectedTypeFilter = '';
    });
    _applyFilters();
  }

  void _clearStatusFilter() {
    setState(() {
      _selectedStatusFilter = '';
    });
    _applyFilters();
  }

  void _clearPriorityFilter() {
    setState(() {
      _selectedPriorityFilter = '';
    });
    _applyFilters();
  }

  void _clearAllFilters() {
    setState(() {
      _selectedTypeFilter = '';
      _selectedStatusFilter = '';
      _selectedPriorityFilter = '';
    });
    _applyFilters();
  }

  void _applyFilters() {
    final reportProvider = Provider.of<ReportProvider>(context, listen: false);
    reportProvider.filterReports(
      type: _selectedTypeFilter.isEmpty ? null : _selectedTypeFilter,
      status: _selectedStatusFilter.isEmpty ? null : _selectedStatusFilter,
      priority: _selectedPriorityFilter.isEmpty
          ? null
          : _selectedPriorityFilter,
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrar Reportes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Tipo:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _selectedTypeFilter.isEmpty ? null : _selectedTypeFilter,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Seleccionar tipo',
              ),
              items: ['', ...AppConstants.reportTypes].map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.isEmpty ? 'Todos' : type),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTypeFilter = value ?? '';
                });
              },
            ),
            const SizedBox(height: 16),
            const Text('Estado:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _selectedStatusFilter.isEmpty
                  ? null
                  : _selectedStatusFilter,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Seleccionar estado',
              ),
              items: ['', ...AppConstants.reportStatuses].map((status) {
                return DropdownMenuItem(
                  value: status,
                  child: Text(status.isEmpty ? 'Todos' : status),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatusFilter = value ?? '';
                });
              },
            ),
            const SizedBox(height: 16),
            const Text('Prioridad:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _selectedPriorityFilter.isEmpty
                  ? null
                  : _selectedPriorityFilter,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Seleccionar prioridad',
              ),
              items: ['', ...AppConstants.reportPriorities].map((priority) {
                return DropdownMenuItem(
                  value: priority,
                  child: Text(priority.isEmpty ? 'Todos' : priority),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPriorityFilter = value ?? '';
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              _applyFilters();
              Navigator.of(context).pop();
            },
            child: const Text('Aplicar'),
          ),
        ],
      ),
    );
  }

  void _navigateToReportForm([ReportModel? report]) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => ReportFormScreen(report: report)),
    );
  }

  void _navigateToQrScanner() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const QrScannerScreen()));
  }

  void _navigateToReportDetail(ReportModel report) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ReportDetailScreen(report: report),
      ),
    );
  }
}
