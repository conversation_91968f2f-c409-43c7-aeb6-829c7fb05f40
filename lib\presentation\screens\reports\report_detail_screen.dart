import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/pdf_service.dart';
import '../../../core/utils/app_utils.dart';
import '../../../data/models/report_model.dart';
import '../../providers/report_provider.dart';
import '../../providers/asset_provider.dart';
import '../assets/asset_detail_screen.dart';
import 'report_form_screen.dart';
import 'pdf_list_screen.dart';

class ReportDetailScreen extends StatefulWidget {
  final ReportModel report;

  const ReportDetailScreen({super.key, required this.report});

  @override
  State<ReportDetailScreen> createState() => _ReportDetailScreenState();
}

class _ReportDetailScreenState extends State<ReportDetailScreen> {
  late ReportModel _currentReport;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentReport = widget.report;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Detalles del Reporte'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.edit), onPressed: _editReport),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'pdf',
                child: Row(
                  children: [
                    Icon(
                      Icons.picture_as_pdf,
                      size: 20,
                      color: AppColors.error,
                    ),
                    SizedBox(width: 8),
                    Text('Generar PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, size: 20),
                    SizedBox(width: 8),
                    Text('Duplicar'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('Eliminar', style: TextStyle(color: AppColors.error)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _refreshReport,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header con estado y prioridad
                    _buildHeader(),
                    const SizedBox(height: 24),

                    // Información básica
                    _buildInfoSection(),
                    const SizedBox(height: 24),

                    // Detalles del activo
                    _buildAssetSection(),
                    const SizedBox(height: 24),

                    // Descripción
                    _buildDescriptionSection(),
                    const SizedBox(height: 24),

                    // Ubicación (si existe)
                    if (_currentReport.location != null) ...[
                      _buildLocationSection(),
                      const SizedBox(height: 24),
                    ],

                    // Imágenes (placeholder)
                    _buildImagesSection(),
                    const SizedBox(height: 24),

                    // Asignación y fechas
                    _buildAssignmentSection(),
                    const SizedBox(height: 24),

                    // Resolución (si existe)
                    if (_currentReport.resolution != null) ...[
                      _buildResolutionSection(),
                      const SizedBox(height: 24),
                    ],

                    // Acciones rápidas
                    _buildQuickActions(),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Título
          Text(
            _currentReport.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          // Estado y Prioridad
          Row(
            children: [
              // Estado
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(_currentReport.status),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _currentReport.status,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Prioridad
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getPriorityColor(_currentReport.priority),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_getPriorityIcon(_currentReport.priority) != null) ...[
                      Icon(
                        _getPriorityIcon(_currentReport.priority),
                        size: 14,
                        color: _getIconColorForPriority(
                          _currentReport.priority,
                        ),
                      ),
                      const SizedBox(width: 4),
                    ],
                    Text(
                      _currentReport.priority,
                      style: TextStyle(
                        color: _getIconColorForPriority(
                          _currentReport.priority,
                        ),
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),

              // Tipo
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.indigo.shade50,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.indigo.shade200),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getTypeIcon(_currentReport.type),
                      size: 14,
                      color: Colors.indigo.shade700,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _currentReport.type,
                      style: TextStyle(
                        color: Colors.indigo.shade700,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return _buildSection(
      title: 'Información General',
      icon: Icons.info_outline,
      child: Column(
        children: [
          _buildInfoRow('ID del Reporte', _currentReport.id),
          _buildInfoRow('Activo', _currentReport.assetName),
          _buildInfoRow('Reportado por', _currentReport.reportedBy),
          _buildInfoRow(
            'Fecha de Reporte',
            AppUtils.formatDate(_currentReport.reportedAt),
          ),
        ],
      ),
    );
  }

  Widget _buildAssetSection() {
    return _buildSection(
      title: 'Activo Relacionado',
      icon: Icons.devices,
      child: InkWell(
        onTap: _navigateToAssetDetail,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.devices, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentReport.assetName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'ID: ${_currentReport.assetId}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap para ver hoja de vida',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return _buildSection(
      title: 'Descripción',
      icon: Icons.description,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Text(
          _currentReport.description,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.textPrimary,
            height: 1.5,
          ),
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return _buildSection(
      title: 'Ubicación',
      icon: Icons.location_on,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange.shade200),
        ),
        child: Row(
          children: [
            Icon(Icons.location_on, color: Colors.orange.shade700),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _currentReport.location!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagesSection() {
    return _buildSection(
      title: 'Imágenes',
      icon: Icons.photo_library,
      child: _buildImagesContent(),
    );
  }

  Widget _buildImagesContent() {
    final hasImages = _currentReport.imageUrls.isNotEmpty;

    if (!hasImages) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          children: [
            Icon(Icons.photo_camera, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 12),
            Text(
              'Sin imágenes adjuntas',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Contador de imágenes
        Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Text(
            '${_currentReport.imageUrls.length} imagen(es) adjunta(s)',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        // Grid de imágenes
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: _currentReport.imageUrls.length,
          itemBuilder: (context, index) {
            return _buildImageThumbnail(_currentReport.imageUrls[index], index);
          },
        ),
      ],
    );
  }

  Widget _buildImageThumbnail(String imagePath, int index) {
    return GestureDetector(
      onTap: () => _viewFullImage(imagePath),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.border),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.file(
            File(imagePath),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Colors.grey.shade200,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.broken_image,
                      color: Colors.grey.shade400,
                      size: 24,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Error',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  void _viewFullImage(String imagePath) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _FullImageViewer(imagePath: imagePath),
      ),
    );
  }

  Widget _buildAssignmentSection() {
    return _buildSection(
      title: 'Asignación y Fechas',
      icon: Icons.assignment_ind,
      child: Column(
        children: [
          if (_currentReport.assignedTo != null) ...[
            _buildInfoRow('Asignado a', _currentReport.assignedTo!),
            if (_currentReport.assignedAt != null)
              _buildInfoRow(
                'Fecha de Asignación',
                AppUtils.formatDate(_currentReport.assignedAt!),
              ),
          ] else
            _buildInfoRow('Asignado a', 'Sin asignar'),

          if (_currentReport.resolvedAt != null)
            _buildInfoRow(
              'Fecha de Resolución',
              AppUtils.formatDate(_currentReport.resolvedAt!),
            ),
        ],
      ),
    );
  }

  Widget _buildResolutionSection() {
    return _buildSection(
      title: 'Resolución',
      icon: Icons.check_circle_outline,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green.shade700),
                const SizedBox(width: 8),
                Text(
                  'Reporte Resuelto',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _currentReport.resolution!,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return _buildSection(
      title: 'Acciones Rápidas',
      icon: Icons.flash_on,
      child: Column(
        children: [
          // Cambiar estado
          _buildActionButton(
            icon: Icons.flag,
            label: 'Cambiar Estado',
            color: AppColors.info,
            onPressed: _showStatusDialog,
          ),
          const SizedBox(height: 12),

          // Asignar responsable
          _buildActionButton(
            icon: Icons.person_add,
            label: 'Asignación Responsable',
            color: AppColors.warning,
            onPressed: _showAssignDialog,
          ),
          const SizedBox(height: 12),

          // Duplicar reporte
          _buildActionButton(
            icon: Icons.copy,
            label: 'Duplicar Reporte',
            color: AppColors.secondary,
            onPressed: _duplicateReport,
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header de la sección
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

          // Contenido de la sección
          Padding(padding: const EdgeInsets.all(16), child: child),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 20),
        label: Text(label),
        style: OutlinedButton.styleFrom(
          foregroundColor: color,
          side: BorderSide(color: color),
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }

  // Métodos de utilidad para colores e iconos
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'abierto':
        return AppColors.info;
      case 'en progreso':
        return AppColors.warning;
      case 'en revisión':
        return Colors.purple;
      case 'resuelto':
        return AppColors.success;
      case 'cerrado':
        return Colors.green.shade900;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.black;
      case 'alta':
        return Colors.red.shade700;
      case 'media':
        return Colors.blue.shade700;
      case 'baja':
        return Colors.amber.shade700;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData? _getPriorityIcon(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Icons.emergency;
      case 'alta':
        return Icons.dangerous;
      case 'media':
        return Icons.warning;
      case 'baja':
        return null;
      default:
        return Icons.priority_high;
    }
  }

  Color _getIconColorForPriority(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.white;
      case 'alta':
        return Colors.white;
      case 'media':
        return Colors.white;
      case 'baja':
        return Colors.black87;
      default:
        return AppColors.textPrimary;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'falla':
        return Icons.broken_image;
      case 'mantenimiento':
        return Icons.build;
      case 'inspección':
        return Icons.back_hand;
      case 'mejora':
        return Icons.trending_up;
      default:
        return Icons.category;
    }
  }

  // Métodos de acción
  Future<void> _refreshReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );
      final updatedReport = await reportProvider.getReportById(
        _currentReport.id,
      );

      if (updatedReport != null) {
        setState(() {
          _currentReport = updatedReport;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al actualizar: ${e.toString()}')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _editReport() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => ReportFormScreen(report: _currentReport),
          ),
        )
        .then((_) {
          // Refrescar cuando regrese del formulario
          _refreshReport();
        });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'pdf':
        _generatePdf();
        break;
      case 'duplicate':
        _duplicateReport();
        break;
      case 'delete':
        _showDeleteDialog();
        break;
    }
  }

  Future<void> _generatePdf() async {
    try {
      // Mostrar indicador de carga
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Generando PDF...'),
            ],
          ),
        ),
      );

      // Generar PDF real usando el servicio
      final pdfFile = await PdfService.generateReportPdf(
        reportType: _currentReport.type,
        reporterName: _currentReport.reportedBy,
        reporterPosition:
            'Técnico de Mantenimiento', // TODO: Obtener del usuario
        location: _currentReport.location ?? 'No especificada',
        category: 'General', // TODO: Agregar categoría al modelo
        assetName: _currentReport.assetName,
        codeOrPlate: _currentReport.assetId, // Usar assetId como código
        priority: _currentReport.priority,
        faults: [
          FaultData(
            title: _currentReport.title,
            description: _currentReport.description,
            system: 'General', // TODO: Agregar sistema al modelo
          ),
        ],
        horometer: null, // TODO: Agregar horómetro al modelo
        kilometrage: null, // TODO: Agregar kilometraje al modelo
      );

      if (mounted) {
        Navigator.of(context).pop(); // Cerrar diálogo de carga

        // Mostrar mensaje de éxito
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('PDF generado: ${pdfFile.path.split('/').last}'),
                ),
              ],
            ),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Ver PDFs',
              textColor: Colors.white,
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const PdfListScreen(),
                  ),
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Cerrar diálogo de carga

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al generar PDF: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showStatusDialog() {
    final statuses = ['Abierto', 'En Progreso', 'Cerrado', 'Cancelado'];
    String selectedStatus = _currentReport.status;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cambiar Estado del Reporte'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Estado actual: ${_currentReport.status}',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: selectedStatus,
              decoration: InputDecoration(
                labelText: 'Nuevo Estado',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.flag, color: AppColors.primary),
              ),
              items: statuses
                  .map(
                    (status) => DropdownMenuItem(
                      value: status,
                      child: Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: _getStatusColor(status),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(status),
                        ],
                      ),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                selectedStatus = value!;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _updateReportStatus(selectedStatus);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary),
            child: const Text('Cambiar', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _updateReportStatus(String newStatus) async {
    if (newStatus == _currentReport.status) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('El estado seleccionado es el mismo que el actual'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );

      // Crear reporte actualizado
      final updatedReport = _currentReport.copyWith(
        status: newStatus,
        resolvedAt: newStatus == 'Cerrado' ? DateTime.now() : null,
      );

      // Actualizar en el provider (simulado)
      await reportProvider.updateReport(updatedReport);

      setState(() {
        _currentReport = updatedReport;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Estado cambiado a: $newStatus'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al cambiar estado: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showAssignDialog() {
    final technicians = [
      'Juan Pérez - Técnico Mecánico',
      'María García - Técnico Eléctrico',
      'Carlos López - Técnico Hidráulico',
      'Ana Martínez - Técnico Electrónico',
      'Luis Rodríguez - Técnico General',
      'Sofia Hernández - Técnico Especialista',
    ];

    String? selectedTechnician = _currentReport.assignedTo;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Asignación de Responsable'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_currentReport.assignedTo != null) ...[
                Text(
                  'Responsable actual: ${_currentReport.assignedTo}',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 16),
              ],

              const Text(
                'Seleccionar nuevo responsable:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 12),

              Container(
                width: double.maxFinite,
                constraints: const BoxConstraints(maxHeight: 200),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Opción para quitar asignación
                      RadioListTile<String?>(
                        title: const Text(
                          'Sin asignar',
                          style: TextStyle(
                            fontStyle: FontStyle.italic,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        value: null,
                        groupValue: selectedTechnician,
                        onChanged: (value) {
                          setDialogState(() {
                            selectedTechnician = value;
                          });
                        },
                        dense: true,
                      ),
                      const Divider(),

                      // Lista de técnicos
                      ...technicians.map(
                        (technician) => RadioListTile<String>(
                          title: Text(technician),
                          value: technician,
                          groupValue: selectedTechnician,
                          onChanged: (value) {
                            setDialogState(() {
                              selectedTechnician = value;
                            });
                          },
                          dense: true,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _updateReportAssignment(selectedTechnician);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: const Text(
                'Asignar',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateReportAssignment(String? newAssignee) async {
    if (newAssignee == _currentReport.assignedTo) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('La asignación seleccionada es la misma que la actual'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );

      // Crear reporte actualizado
      final updatedReport = _currentReport.copyWith(
        assignedTo: newAssignee,
        assignedAt: newAssignee != null ? DateTime.now() : null,
      );

      // Actualizar en el provider (simulado)
      await reportProvider.updateReport(updatedReport);

      setState(() {
        _currentReport = updatedReport;
      });

      if (mounted) {
        final message = newAssignee != null
            ? 'Reporte asignado a: $newAssignee'
            : 'Asignación removida del reporte';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message), backgroundColor: AppColors.success),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al actualizar asignación: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _duplicateReport() {
    try {
      // Crear un nuevo reporte basado en el actual
      final duplicatedReport = ReportModel(
        id: '', // Nuevo ID se generará automáticamente
        assetId: _currentReport.assetId,
        assetName: _currentReport.assetName,
        title: 'Copia - ${_currentReport.title}',
        description: _currentReport.description,
        priority: _currentReport.priority,
        status: 'Abierto',
        type: _currentReport.type,
        imageUrls: List.from(_currentReport.imageUrls), // Copiar imágenes
        location: _currentReport.location,
        reportedAt: DateTime.now(),
        reportedBy: _currentReport.reportedBy,
        assignedTo: null, // Sin asignar
        assignedAt: null,
        resolvedAt: null,
        resolution: null,
        metadata: _currentReport.metadata != null
            ? Map<String, dynamic>.from(_currentReport.metadata!)
            : null,
      );

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ReportFormScreen(report: duplicatedReport),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error al duplicar reporte: ${e.toString()}'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Reporte'),
        content: const Text(
          '¿Estás seguro de que quieres eliminar este reporte? Esta acción no se puede deshacer.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteReport();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text(
              'Eliminar',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );
      final success = await reportProvider.deleteReport(_currentReport.id);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reporte eliminado exitosamente'),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop(); // Regresar a la lista
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Error al eliminar el reporte'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _navigateToAssetDetail() async {
    try {
      // Obtener el activo completo usando el AssetProvider
      final assetProvider = Provider.of<AssetProvider>(context, listen: false);

      // Buscar el activo por ID
      final asset = assetProvider.assets.firstWhere(
        (asset) => asset.id == _currentReport.assetId,
        orElse: () => throw Exception('Activo no encontrado'),
      );

      // Navegar a la hoja de vida del activo
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => AssetDetailScreen(asset: asset),
        ),
      );
    } catch (e) {
      // Si no se encuentra el activo, mostrar mensaje de error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No se pudo cargar el activo: ${e.toString()}'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}

// Pantalla para ver imagen en pantalla completa
class _FullImageViewer extends StatelessWidget {
  final String imagePath;

  const _FullImageViewer({required this.imagePath});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: const Text('Vista de Imagen'),
      ),
      body: Center(
        child: InteractiveViewer(
          child: Image.file(
            File(imagePath),
            errorBuilder: (context, error, stackTrace) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.broken_image, color: Colors.white, size: 64),
                  const SizedBox(height: 16),
                  const Text(
                    'Error al cargar la imagen',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
