import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/app_utils.dart';
import '../../../data/models/report_model.dart';
import '../../providers/report_provider.dart';
import 'report_form_screen.dart';

class ReportDetailScreen extends StatefulWidget {
  final ReportModel report;

  const ReportDetailScreen({super.key, required this.report});

  @override
  State<ReportDetailScreen> createState() => _ReportDetailScreenState();
}

class _ReportDetailScreenState extends State<ReportDetailScreen> {
  late ReportModel _currentReport;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentReport = widget.report;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Detalles del Reporte'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.edit), onPressed: _editReport),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, size: 20),
                    SizedBox(width: 8),
                    Text('Duplicar'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('Eliminar', style: TextStyle(color: AppColors.error)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _refreshReport,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header con estado y prioridad
                    _buildHeader(),
                    const SizedBox(height: 24),

                    // Información básica
                    _buildInfoSection(),
                    const SizedBox(height: 24),

                    // Detalles del activo
                    _buildAssetSection(),
                    const SizedBox(height: 24),

                    // Descripción
                    _buildDescriptionSection(),
                    const SizedBox(height: 24),

                    // Ubicación (si existe)
                    if (_currentReport.location != null) ...[
                      _buildLocationSection(),
                      const SizedBox(height: 24),
                    ],

                    // Imágenes (placeholder)
                    _buildImagesSection(),
                    const SizedBox(height: 24),

                    // Asignación y fechas
                    _buildAssignmentSection(),
                    const SizedBox(height: 24),

                    // Resolución (si existe)
                    if (_currentReport.resolution != null) ...[
                      _buildResolutionSection(),
                      const SizedBox(height: 24),
                    ],

                    // Acciones rápidas
                    _buildQuickActions(),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Título
          Text(
            _currentReport.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          // Estado y Prioridad
          Row(
            children: [
              // Estado
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(_currentReport.status),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _currentReport.status,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Prioridad
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getPriorityColor(_currentReport.priority),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_getPriorityIcon(_currentReport.priority) != null) ...[
                      Icon(
                        _getPriorityIcon(_currentReport.priority),
                        size: 14,
                        color: _getIconColorForPriority(
                          _currentReport.priority,
                        ),
                      ),
                      const SizedBox(width: 4),
                    ],
                    Text(
                      _currentReport.priority,
                      style: TextStyle(
                        color: _getIconColorForPriority(
                          _currentReport.priority,
                        ),
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),

              // Tipo
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.indigo.shade50,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.indigo.shade200),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getTypeIcon(_currentReport.type),
                      size: 14,
                      color: Colors.indigo.shade700,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _currentReport.type,
                      style: TextStyle(
                        color: Colors.indigo.shade700,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return _buildSection(
      title: 'Información General',
      icon: Icons.info_outline,
      child: Column(
        children: [
          _buildInfoRow('ID del Reporte', _currentReport.id),
          _buildInfoRow('Activo', _currentReport.assetName),
          _buildInfoRow('Reportado por', _currentReport.reportedBy),
          _buildInfoRow(
            'Fecha de Reporte',
            AppUtils.formatDate(_currentReport.reportedAt),
          ),
        ],
      ),
    );
  }

  Widget _buildAssetSection() {
    return _buildSection(
      title: 'Activo Relacionado',
      icon: Icons.devices,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.devices, color: Colors.white, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _currentReport.assetName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'ID: ${_currentReport.assetId}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () {
                // TODO: Navegar a detalles del activo
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Detalles del activo próximamente'),
                  ),
                );
              },
              icon: const Icon(Icons.arrow_forward_ios, size: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return _buildSection(
      title: 'Descripción',
      icon: Icons.description,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Text(
          _currentReport.description,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.textPrimary,
            height: 1.5,
          ),
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return _buildSection(
      title: 'Ubicación',
      icon: Icons.location_on,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange.shade200),
        ),
        child: Row(
          children: [
            Icon(Icons.location_on, color: Colors.orange.shade700),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _currentReport.location!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagesSection() {
    return _buildSection(
      title: 'Imágenes',
      icon: Icons.photo_library,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey.shade200,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          children: [
            Icon(Icons.photo_camera, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 12),
            Text(
              'Sin imágenes adjuntas',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Próximamente: Visualización de imágenes',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignmentSection() {
    return _buildSection(
      title: 'Asignación y Fechas',
      icon: Icons.assignment_ind,
      child: Column(
        children: [
          if (_currentReport.assignedTo != null) ...[
            _buildInfoRow('Asignado a', _currentReport.assignedTo!),
            if (_currentReport.assignedAt != null)
              _buildInfoRow(
                'Fecha de Asignación',
                AppUtils.formatDate(_currentReport.assignedAt!),
              ),
          ] else
            _buildInfoRow('Asignado a', 'Sin asignar'),

          if (_currentReport.resolvedAt != null)
            _buildInfoRow(
              'Fecha de Resolución',
              AppUtils.formatDate(_currentReport.resolvedAt!),
            ),
        ],
      ),
    );
  }

  Widget _buildResolutionSection() {
    return _buildSection(
      title: 'Resolución',
      icon: Icons.check_circle_outline,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green.shade700),
                const SizedBox(width: 8),
                Text(
                  'Reporte Resuelto',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _currentReport.resolution!,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return _buildSection(
      title: 'Acciones Rápidas',
      icon: Icons.flash_on,
      child: Column(
        children: [
          // Cambiar estado
          _buildActionButton(
            icon: Icons.flag,
            label: 'Cambiar Estado',
            color: AppColors.info,
            onPressed: _showStatusDialog,
          ),
          const SizedBox(height: 12),

          // Asignar técnico
          _buildActionButton(
            icon: Icons.person_add,
            label: 'Asignar Técnico',
            color: AppColors.warning,
            onPressed: _showAssignDialog,
          ),
          const SizedBox(height: 12),

          // Duplicar reporte
          _buildActionButton(
            icon: Icons.copy,
            label: 'Duplicar Reporte',
            color: AppColors.secondary,
            onPressed: _duplicateReport,
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header de la sección
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),

          // Contenido de la sección
          Padding(padding: const EdgeInsets.all(16), child: child),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 20),
        label: Text(label),
        style: OutlinedButton.styleFrom(
          foregroundColor: color,
          side: BorderSide(color: color),
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }

  // Métodos de utilidad para colores e iconos
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'abierto':
        return AppColors.info;
      case 'en progreso':
        return AppColors.warning;
      case 'en revisión':
        return Colors.purple;
      case 'resuelto':
        return AppColors.success;
      case 'cerrado':
        return Colors.green.shade900;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.black;
      case 'alta':
        return Colors.red.shade700;
      case 'media':
        return Colors.blue.shade700;
      case 'baja':
        return Colors.amber.shade700;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData? _getPriorityIcon(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Icons.emergency;
      case 'alta':
        return Icons.dangerous;
      case 'media':
        return Icons.warning;
      case 'baja':
        return null;
      default:
        return Icons.priority_high;
    }
  }

  Color _getIconColorForPriority(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.white;
      case 'alta':
        return Colors.white;
      case 'media':
        return Colors.white;
      case 'baja':
        return Colors.black87;
      default:
        return AppColors.textPrimary;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'falla':
        return Icons.broken_image;
      case 'mantenimiento':
        return Icons.build;
      case 'inspección':
        return Icons.back_hand;
      case 'mejora':
        return Icons.trending_up;
      default:
        return Icons.category;
    }
  }

  // Métodos de acción
  Future<void> _refreshReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );
      final updatedReport = await reportProvider.getReportById(
        _currentReport.id,
      );

      if (updatedReport != null) {
        setState(() {
          _currentReport = updatedReport;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al actualizar: ${e.toString()}')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _editReport() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => ReportFormScreen(report: _currentReport),
          ),
        )
        .then((_) {
          // Refrescar cuando regrese del formulario
          _refreshReport();
        });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'duplicate':
        _duplicateReport();
        break;
      case 'delete':
        _showDeleteDialog();
        break;
    }
  }

  void _showStatusDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cambiar Estado'),
        content: const Text('Funcionalidad próximamente disponible'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  void _showAssignDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Asignar Técnico'),
        content: const Text('Funcionalidad próximamente disponible'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  void _duplicateReport() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ReportFormScreen(
          report: _currentReport.copyWith(
            id: '', // Nuevo ID se generará automáticamente
            reportedAt: DateTime.now(),
            status: 'Abierto',
            assignedTo: null,
            assignedAt: null,
            resolvedAt: null,
            resolution: null,
          ),
        ),
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Reporte'),
        content: const Text(
          '¿Estás seguro de que quieres eliminar este reporte? Esta acción no se puede deshacer.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteReport();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text(
              'Eliminar',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );
      final success = await reportProvider.deleteReport(_currentReport.id);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reporte eliminado exitosamente'),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop(); // Regresar a la lista
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Error al eliminar el reporte'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
