import 'package:flutter/foundation.dart';

// Mock service para Android cuando Appwrite tiene problemas
class MockAppwriteService {
  static MockAppwriteService? _instance;
  
  MockAppwriteService._internal();

  static MockAppwriteService get instance {
    _instance ??= MockAppwriteService._internal();
    return _instance!;
  }

  // Mock client config
  Map<String, String> get clientConfig => {
    'endpoint': 'https://mock.appwrite.io/v1',
    'project': 'mock-project-id',
  };

  // Método para inicializar el servicio mock
  static Future<void> initialize() async {
    MockAppwriteService.instance;
    if (kDebugMode) {
      print('🔧 MockAppwriteService initialized for Android development');
    }
  }

  // Mock para verificar si está logueado
  Future<bool> isLoggedIn() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return false; // Siempre retorna false en mock
  }

  // Mock para obtener usuario actual
  Future<MockUser?> getCurrentUser() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return null; // Siempre retorna null en mock
  }

  // Mock account
  MockAccount get account => MockAccount();
  
  // Mock databases
  MockDatabases get databases => MockDatabases();
  
  // Mock storage
  MockStorage get storage => MockStorage();
}

// Mock classes para simular la API de Appwrite
class MockAccount {
  Future<MockUser> get() async {
    await Future.delayed(const Duration(milliseconds: 500));
    throw Exception('Mock: Usuario no autenticado');
  }

  Future<MockSession> createEmailSession({
    required String email,
    required String password,
  }) async {
    await Future.delayed(const Duration(seconds: 1));
    if (email == '<EMAIL>' && password == 'password') {
      return MockSession(userId: 'mock-user-id');
    }
    throw Exception('Mock: Credenciales inválidas');
  }

  Future<MockUser> create({
    required String userId,
    required String email,
    required String password,
    String? name,
  }) async {
    await Future.delayed(const Duration(seconds: 1));
    return MockUser(id: userId, email: email, name: name);
  }

  Future<void> deleteSession({required String sessionId}) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // Mock logout
  }

  Future<void> createRecovery({
    required String email,
    required String url,
  }) async {
    await Future.delayed(const Duration(seconds: 1));
    // Mock password recovery
  }
}

class MockDatabases {
  Future<MockDocumentList> listDocuments({
    required String databaseId,
    required String collectionId,
    List<String>? queries,
  }) async {
    await Future.delayed(const Duration(seconds: 1));
    return MockDocumentList(documents: []);
  }

  Future<MockDocument> createDocument({
    required String databaseId,
    required String collectionId,
    required String documentId,
    required Map<String, dynamic> data,
  }) async {
    await Future.delayed(const Duration(seconds: 1));
    return MockDocument(id: documentId, data: data);
  }

  Future<MockDocument> updateDocument({
    required String databaseId,
    required String collectionId,
    required String documentId,
    required Map<String, dynamic> data,
  }) async {
    await Future.delayed(const Duration(seconds: 1));
    return MockDocument(id: documentId, data: data);
  }

  Future<void> deleteDocument({
    required String databaseId,
    required String collectionId,
    required String documentId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // Mock delete
  }

  Future<MockDocument> getDocument({
    required String databaseId,
    required String collectionId,
    required String documentId,
  }) async {
    await Future.delayed(const Duration(seconds: 1));
    return MockDocument(id: documentId, data: {'mock': 'data'});
  }
}

class MockStorage {
  // Mock storage methods if needed
}

// Mock data classes
class MockUser {
  final String id;
  final String email;
  final String? name;

  MockUser({required this.id, required this.email, this.name});

  // Simular la propiedad $id de Appwrite
  String get $id => id;
}

class MockSession {
  final String userId;

  MockSession({required this.userId});
}

class MockDocument {
  final String id;
  final Map<String, dynamic> data;

  MockDocument({required this.id, required this.data});
}

class MockDocumentList {
  final List<MockDocument> documents;

  MockDocumentList({required this.documents});
}
