import 'mock_appwrite_service.dart';

// Servicio adaptador que usa Mock para desarrollo
class BackendService {
  static BackendService? _instance;
  static bool _useAppwrite = false; // Temporalmente siempre false

  BackendService._internal();

  static BackendService get instance {
    _instance ??= BackendService._internal();
    return _instance!;
  }

  static Future<void> initialize() async {
    // Por ahora, siempre usar mock para evitar problemas de compatibilidad
    _useAppwrite = false;
    await MockAppwriteService.initialize();
  }

  // Getters que retornan el servicio mock
  dynamic get client {
    return {'config': MockAppwriteService.instance.clientConfig};
  }

  dynamic get account {
    return MockAppwriteService.instance.account;
  }

  dynamic get databases {
    return MockAppwriteService.instance.databases;
  }

  dynamic get storage {
    return MockAppwriteService.instance.storage;
  }

  // Métodos de utilidad
  Future<bool> isLoggedIn() async {
    return await MockAppwriteService.instance.isLoggedIn();
  }

  Future<dynamic> getCurrentUser() async {
    return await MockAppwriteService.instance.getCurrentUser();
  }

  // Información del servicio actual
  String get serviceName => _useAppwrite ? 'Appwrite' : 'Mock';
  bool get isUsingAppwrite => _useAppwrite;
  bool get isUsingMock => !_useAppwrite;
}
