class MaintenanceModel {
  final String id;
  final String assetId;
  final String assetName;
  final String title;
  final String description;
  final String type; // Preventivo, Correctivo, Predictivo, Emergencia
  final String status; // Programado, En Progreso, Completado, Cancelado
  final String priority; // Baja, Media, Alta, Crítica
  final DateTime scheduledDate;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final String? assignedTo;
  final String? performedBy;
  final String? notes;
  final List<String> tasksCompleted;
  final List<String> partsUsed;
  final double? cost;
  final int? durationMinutes;
  final String? nextMaintenanceDate;
  final List<String> imageUrls;
  final Map<String, dynamic>? metadata;

  const MaintenanceModel({
    required this.id,
    required this.assetId,
    required this.assetName,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    required this.priority,
    required this.scheduledDate,
    this.startedAt,
    this.completedAt,
    this.assignedTo,
    this.performedBy,
    this.notes,
    this.tasksCompleted = const [],
    this.partsUsed = const [],
    this.cost,
    this.durationMinutes,
    this.nextMaintenanceDate,
    this.imageUrls = const [],
    this.metadata,
  });

  factory MaintenanceModel.fromJson(Map<String, dynamic> json) {
    return MaintenanceModel(
      id: json['id'] as String,
      assetId: json['asset_id'] as String,
      assetName: json['asset_name'] as String? ?? '',
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      status: json['status'] as String,
      priority: json['priority'] as String,
      scheduledDate: DateTime.parse(json['scheduled_date'] as String),
      startedAt: json['started_at'] != null
          ? DateTime.parse(json['started_at'] as String)
          : null,
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'] as String)
          : null,
      assignedTo: json['assigned_to'] as String?,
      performedBy: json['performed_by'] as String?,
      notes: json['notes'] as String?,
      tasksCompleted: json['tasks_completed'] != null
          ? List<String>.from(json['tasks_completed'] as List)
          : [],
      partsUsed: json['parts_used'] != null
          ? List<String>.from(json['parts_used'] as List)
          : [],
      cost: json['cost'] as double?,
      durationMinutes: json['duration_minutes'] as int?,
      nextMaintenanceDate: json['next_maintenance_date'] as String?,
      imageUrls: json['image_urls'] != null
          ? List<String>.from(json['image_urls'] as List)
          : [],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'asset_id': assetId,
      'asset_name': assetName,
      'title': title,
      'description': description,
      'type': type,
      'status': status,
      'priority': priority,
      'scheduled_date': scheduledDate.toIso8601String(),
      'started_at': startedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'assigned_to': assignedTo,
      'performed_by': performedBy,
      'notes': notes,
      'tasks_completed': tasksCompleted,
      'parts_used': partsUsed,
      'cost': cost,
      'duration_minutes': durationMinutes,
      'next_maintenance_date': nextMaintenanceDate,
      'image_urls': imageUrls,
      'metadata': metadata,
    };
  }

  MaintenanceModel copyWith({
    String? id,
    String? assetId,
    String? assetName,
    String? title,
    String? description,
    String? type,
    String? status,
    String? priority,
    DateTime? scheduledDate,
    DateTime? startedAt,
    DateTime? completedAt,
    String? assignedTo,
    String? performedBy,
    String? notes,
    List<String>? tasksCompleted,
    List<String>? partsUsed,
    double? cost,
    int? durationMinutes,
    String? nextMaintenanceDate,
    List<String>? imageUrls,
    Map<String, dynamic>? metadata,
  }) {
    return MaintenanceModel(
      id: id ?? this.id,
      assetId: assetId ?? this.assetId,
      assetName: assetName ?? this.assetName,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      assignedTo: assignedTo ?? this.assignedTo,
      performedBy: performedBy ?? this.performedBy,
      notes: notes ?? this.notes,
      tasksCompleted: tasksCompleted ?? this.tasksCompleted,
      partsUsed: partsUsed ?? this.partsUsed,
      cost: cost ?? this.cost,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      nextMaintenanceDate: nextMaintenanceDate ?? this.nextMaintenanceDate,
      imageUrls: imageUrls ?? this.imageUrls,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'MaintenanceModel(id: $id, title: $title, type: $type, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MaintenanceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
