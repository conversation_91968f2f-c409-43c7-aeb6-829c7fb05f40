class AppStrings {
  // General
  static const String appName = 'MTTO 60';
  static const String loading = 'Cargando...';
  static const String error = 'Error';
  static const String success = 'Éxito';
  static const String cancel = 'Cancelar';
  static const String save = 'Guardar';
  static const String delete = 'Eliminar';
  static const String edit = 'Editar';
  static const String add = 'Agregar';
  static const String search = 'Buscar';
  static const String filter = 'Filtrar';
  static const String refresh = 'Actualizar';
  static const String noData = 'No hay datos disponibles';
  static const String retry = 'Reintentar';

  // Authentication
  static const String login = 'Iniciar Sesión';
  static const String logout = 'Cerrar Sesión';
  static const String register = 'Registrarse';
  static const String email = 'Correo Electrónico';
  static const String password = 'Contraseña';
  static const String confirmPassword = 'Confirmar Contraseña';
  static const String forgotPassword = 'Olvidé mi contraseña';
  static const String resetPassword = 'Restablecer contraseña';
  static const String loginSuccess = 'Inicio de sesión exitoso';
  static const String loginError = 'Error al iniciar sesión';
  static const String invalidCredentials = 'Credenciales inválidas';

  // Navigation
  static const String dashboard = 'Dashboard';
  static const String assets = 'Activos';
  static const String reports = 'Reportes';
  static const String workOrders = 'Órdenes de Trabajo';
  static const String maintenance = 'Mantenimiento';
  static const String settings = 'Configuración';
  static const String profile = 'Perfil';

  // Assets
  static const String assetList = 'Lista de Activos';
  static const String assetDetails = 'Detalles del Activo';
  static const String addAsset = 'Agregar Activo';
  static const String editAsset = 'Editar Activo';
  static const String assetName = 'Nombre del Activo';
  static const String assetType = 'Tipo de Activo';
  static const String assetCode = 'Código del Activo';
  static const String assetDescription = 'Descripción';
  static const String assetLocation = 'Ubicación';
  static const String assetStatus = 'Estado';
  static const String assetHistory = 'Historial';

  // Reports
  static const String reportIssue = 'Reportar Problema';
  static const String reportDetails = 'Detalles del Reporte';
  static const String reportDescription = 'Descripción del Problema';
  static const String reportPriority = 'Prioridad';
  static const String reportStatus = 'Estado del Reporte';
  static const String reportDate = 'Fecha del Reporte';
  static const String reportedBy = 'Reportado por';

  // Work Orders
  static const String workOrderList = 'Lista de Órdenes';
  static const String workOrderDetails = 'Detalles de la Orden';
  static const String createWorkOrder = 'Crear Orden de Trabajo';
  static const String assignTechnician = 'Asignar Técnico';
  static const String workOrderStatus = 'Estado de la Orden';
  static const String estimatedTime = 'Tiempo Estimado';
  static const String actualTime = 'Tiempo Real';
  static const String materials = 'Materiales';
  static const String notes = 'Notas';

  // Maintenance
  static const String preventiveMaintenance = 'Mantenimiento Preventivo';
  static const String correctiveMaintenance = 'Mantenimiento Correctivo';
  static const String maintenanceSchedule = 'Programación de Mantenimiento';
  static const String nextMaintenance = 'Próximo Mantenimiento';
  static const String maintenanceHistory = 'Historial de Mantenimiento';

  // Validation Messages
  static const String fieldRequired = 'Este campo es requerido';
  static const String invalidEmail = 'Correo electrónico inválido';
  static const String passwordTooShort = 'La contraseña debe tener al menos 6 caracteres';
  static const String passwordsDoNotMatch = 'Las contraseñas no coinciden';

  // Error Messages
  static const String networkError = 'Error de conexión a internet';
  static const String serverError = 'Error del servidor';
  static const String unknownError = 'Error desconocido';
  static const String noInternetConnection = 'Sin conexión a internet';
}
