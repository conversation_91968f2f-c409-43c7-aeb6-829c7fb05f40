import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/glass_card.dart';
import '../../../core/widgets/skeleton_loader.dart';
import '../../../data/models/asset_model.dart';
import '../../providers/asset_provider.dart';
import 'asset_detail_screen.dart';
import 'asset_form_screen.dart';

class AssetListScreen extends StatefulWidget {
  const AssetListScreen({super.key});

  @override
  State<AssetListScreen> createState() => _AssetListScreenState();
}

class _AssetListScreenState extends State<AssetListScreen> {
  final _searchController = TextEditingController();
  String _selectedTypeFilter = '';
  String _selectedStatusFilter = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AssetProvider>(context, listen: false).loadAssets();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(AppStrings.assets),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<AssetProvider>(context, listen: false).loadAssets();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Barra de búsqueda
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.white,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Buscar activos...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _updateSearch('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.border),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.primary),
                ),
              ),
              onChanged: _updateSearch,
            ),
          ),

          // Filtros activos
          if (_selectedTypeFilter.isNotEmpty ||
              _selectedStatusFilter.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              color: Colors.grey.shade100,
              child: Row(
                children: [
                  const Text(
                    'Filtros: ',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  if (_selectedTypeFilter.isNotEmpty) ...[
                    Chip(
                      label: Text(_selectedTypeFilter),
                      onDeleted: () => _clearTypeFilter(),
                      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                    ),
                    const SizedBox(width: 8),
                  ],
                  if (_selectedStatusFilter.isNotEmpty) ...[
                    Chip(
                      label: Text(_selectedStatusFilter),
                      onDeleted: () => _clearStatusFilter(),
                      backgroundColor: AppColors.secondary.withValues(
                        alpha: 0.1,
                      ),
                    ),
                  ],
                  const Spacer(),
                  TextButton(
                    onPressed: _clearAllFilters,
                    child: const Text('Limpiar todo'),
                  ),
                ],
              ),
            ),

          // Lista de activos
          Expanded(
            child: Consumer<AssetProvider>(
              builder: (context, assetProvider, _) {
                if (assetProvider.isLoading) {
                  return const SkeletonList(itemCount: 6);
                }

                if (assetProvider.errorMessage != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppColors.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          assetProvider.errorMessage!,
                          style: const TextStyle(
                            fontSize: 16,
                            color: AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => assetProvider.loadAssets(),
                          child: const Text('Reintentar'),
                        ),
                      ],
                    ),
                  );
                }

                final assets = assetProvider.filteredAssets;

                if (assets.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.inventory_2_outlined,
                          size: 64,
                          color: AppColors.textHint,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'No hay activos disponibles',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Agrega tu primer activo para comenzar',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textHint,
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: () => _navigateToAssetForm(),
                          icon: const Icon(Icons.add),
                          label: const Text('Agregar Activo'),
                        ),
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => assetProvider.loadAssets(),
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: assets.length,
                    itemBuilder: (context, index) {
                      final asset = assets[index];
                      return _buildAssetCard(asset);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAssetForm,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAssetCard(AssetModel asset) {
    return GlassCard(
      margin: const EdgeInsets.only(bottom: 12.0),
      isInteractive: true,
      onTap: () => _navigateToAssetDetail(asset),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getStatusColor(asset.status).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getAssetIcon(asset.type),
                  color: _getStatusColor(asset.status),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      asset.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.getTextPrimary(context),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Código: ${asset.code}',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.getTextSecondary(context),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(asset.status),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  asset.status,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoChip(
                  icon: Icons.category,
                  label: asset.type,
                  color: AppColors.info,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildInfoChip(
                  icon: Icons.location_on,
                  label: asset.location ?? 'Sin ubicación',
                  color: AppColors.secondary,
                ),
              ),
            ],
          ),
          if (asset.description?.isNotEmpty == true) ...[
            const SizedBox(height: 8),
            Text(
              asset.description!,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getAssetIcon(String type) {
    switch (type.toLowerCase()) {
      case 'maquinaria':
        return Icons.precision_manufacturing;
      case 'vehículo':
        return Icons.directions_car;
      case 'equipo':
        return Icons.build_circle;
      case 'herramienta':
        return Icons.build;
      case 'mobiliario':
        return Icons.chair;
      default:
        return Icons.inventory_2;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'operativo':
        return AppColors.success;
      case 'mantenimiento':
        return AppColors.warning;
      case 'fuera de servicio':
        return AppColors.error;
      case 'en reparación':
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }

  void _updateSearch(String query) {
    final assetProvider = Provider.of<AssetProvider>(context, listen: false);
    assetProvider.searchAssets(query);
  }

  void _clearTypeFilter() {
    setState(() {
      _selectedTypeFilter = '';
    });
    _applyFilters();
  }

  void _clearStatusFilter() {
    setState(() {
      _selectedStatusFilter = '';
    });
    _applyFilters();
  }

  void _clearAllFilters() {
    setState(() {
      _selectedTypeFilter = '';
      _selectedStatusFilter = '';
    });
    _applyFilters();
  }

  void _applyFilters() {
    final assetProvider = Provider.of<AssetProvider>(context, listen: false);
    assetProvider.filterAssets(
      type: _selectedTypeFilter.isEmpty ? null : _selectedTypeFilter,
      status: _selectedStatusFilter.isEmpty ? null : _selectedStatusFilter,
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrar Activos'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Tipo de Activo:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _selectedTypeFilter.isEmpty ? null : _selectedTypeFilter,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Seleccionar tipo',
              ),
              items: ['', ...AppConstants.assetTypes].map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.isEmpty ? 'Todos' : type),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTypeFilter = value ?? '';
                });
              },
            ),
            const SizedBox(height: 16),
            const Text('Estado:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _selectedStatusFilter.isEmpty
                  ? null
                  : _selectedStatusFilter,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Seleccionar estado',
              ),
              items: ['', ...AppConstants.assetStatuses].map((status) {
                return DropdownMenuItem(
                  value: status,
                  child: Text(status.isEmpty ? 'Todos' : status),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatusFilter = value ?? '';
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              _applyFilters();
              Navigator.of(context).pop();
            },
            child: const Text('Aplicar'),
          ),
        ],
      ),
    );
  }

  void _navigateToAssetForm([AssetModel? asset]) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => AssetFormScreen(asset: asset)),
    );
  }

  void _navigateToAssetDetail(AssetModel asset) {
    print(
      '🔍 Navegando a hoja de vida del activo: ${asset.name} (ID: ${asset.id})',
    );
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => AssetDetailScreen(asset: asset)),
    );
  }
}
