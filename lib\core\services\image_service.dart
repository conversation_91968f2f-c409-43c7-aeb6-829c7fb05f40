import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

class ImageService {
  static final ImagePicker _picker = ImagePicker();
  static const Uuid _uuid = Uuid();

  /// Muestra un diálogo para seleccionar la fuente de la imagen
  static Future<File?> pickImage(BuildContext context) async {
    return await showModalBottomSheet<File?>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            
            // <PERSON><PERSON><PERSON>lo
            const Text(
              'Seleccionar Imagen',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // Opciones
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Cámara
                _buildImageSourceOption(
                  context: context,
                  icon: Icons.camera_alt,
                  label: 'Cámara',
                  onTap: () async {
                    Navigator.pop(context);
                    final file = await _pickFromCamera();
                    if (context.mounted) {
                      Navigator.pop(context, file);
                    }
                  },
                ),
                
                // Galería
                _buildImageSourceOption(
                  context: context,
                  icon: Icons.photo_library,
                  label: 'Galería',
                  onTap: () async {
                    Navigator.pop(context);
                    final file = await _pickFromGallery();
                    if (context.mounted) {
                      Navigator.pop(context, file);
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  static Widget _buildImageSourceOption({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 40,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Captura imagen desde la cámara
  static Future<File?> _pickFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      if (image != null) {
        return await _saveImageToAppDirectory(File(image.path));
      }
      return null;
    } catch (e) {
      debugPrint('Error al capturar imagen: $e');
      return null;
    }
  }

  /// Selecciona imagen desde la galería
  static Future<File?> _pickFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      if (image != null) {
        return await _saveImageToAppDirectory(File(image.path));
      }
      return null;
    } catch (e) {
      debugPrint('Error al seleccionar imagen: $e');
      return null;
    }
  }

  /// Selecciona múltiples imágenes desde la galería
  static Future<List<File>> pickMultipleImages() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      List<File> savedImages = [];
      for (XFile image in images) {
        final savedImage = await _saveImageToAppDirectory(File(image.path));
        if (savedImage != null) {
          savedImages.add(savedImage);
        }
      }
      
      return savedImages;
    } catch (e) {
      debugPrint('Error al seleccionar múltiples imágenes: $e');
      return [];
    }
  }

  /// Guarda la imagen en el directorio de la aplicación
  static Future<File?> _saveImageToAppDirectory(File originalFile) async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String imagesDir = path.join(appDir.path, 'images');
      
      // Crear directorio si no existe
      await Directory(imagesDir).create(recursive: true);
      
      // Generar nombre único para la imagen
      final String fileName = '${_uuid.v4()}.jpg';
      final String newPath = path.join(imagesDir, fileName);
      
      // Copiar archivo
      final File newFile = await originalFile.copy(newPath);
      
      return newFile;
    } catch (e) {
      debugPrint('Error al guardar imagen: $e');
      return null;
    }
  }

  /// Elimina una imagen del sistema de archivos
  static Future<bool> deleteImage(String imagePath) async {
    try {
      final File file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error al eliminar imagen: $e');
      return false;
    }
  }

  /// Obtiene el tamaño de una imagen en bytes
  static Future<int> getImageSize(String imagePath) async {
    try {
      final File file = File(imagePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      debugPrint('Error al obtener tamaño de imagen: $e');
      return 0;
    }
  }

  /// Formatea el tamaño de archivo en formato legible
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  /// Limpia imágenes temporales (opcional)
  static Future<void> cleanupTempImages() async {
    try {
      final Directory tempDir = await getTemporaryDirectory();
      final List<FileSystemEntity> files = tempDir.listSync();
      
      for (FileSystemEntity file in files) {
        if (file is File && file.path.endsWith('.jpg')) {
          await file.delete();
        }
      }
    } catch (e) {
      debugPrint('Error al limpiar imágenes temporales: $e');
    }
  }
}
