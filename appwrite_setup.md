# Configuración de Appwrite para MTTO 60

## 1. <PERSON>rear Proyecto en Appwrite

### Opción A: Appwrite Cloud (Recomendado)
1. Ve a [cloud.appwrite.io](https://cloud.appwrite.io)
2. Crea una nueva cuenta o inicia sesión
3. Crea un nuevo proyecto
4. Anota el Project ID y Endpoint

### Opción B: Appwrite Self-Hosted
1. Instala Docker en tu servidor
2. Ejecuta: `docker run -it --rm --volume /var/run/docker.sock:/var/run/docker.sock --volume "$(pwd)"/appwrite:/usr/src/code/appwrite:rw --entrypoint="install" appwrite/appwrite:1.4.13`
3. Sigue las instrucciones de instalación
4. Accede a tu instancia de Appwrite

## 2. Configurar Variables de Entorno

Actualiza el archivo `lib/core/constants/app_constants.dart`:

```dart
static const String appwriteEndpoint = 'TU_APPWRITE_ENDPOINT_AQUI';
static const String appwriteProjectId = 'TU_APPWRITE_PROJECT_ID_AQUI';
static const String appwriteDatabaseId = 'TU_APPWRITE_DATABASE_ID_AQUI';
```

## 3. Crear Base de Datos y Colecciones

### 3.1. Crear Base de Datos
1. Ve a la sección "Databases" en tu proyecto Appwrite
2. Crea una nueva base de datos llamada "mtto_60"
3. Anota el Database ID generado

### 3.2. Configurar Autenticación
1. Ve a la sección "Auth" en tu proyecto
2. Habilita "Email/Password" authentication
3. Configura las URLs de redirección si es necesario

### 3.3. Crear Colecciones

#### Colección: users
1. Crea una nueva colección llamada "users"
2. Configura los siguientes atributos:
   - `email` (String, 255, Required)
   - `name` (String, 100, Optional)
   - `phone` (String, 20, Optional)
   - `role` (String, 50, Required, Default: "Reportador")
   - `avatar_url` (String, 500, Optional)
   - `created_at` (DateTime, Required)
   - `updated_at` (DateTime, Optional)
   - `is_active` (Boolean, Required, Default: true)

3. Configurar permisos:
   - Read: `users`
   - Create: `users`
   - Update: `users`
   - Delete: `admins`

#### Colección: assets
1. Crea una nueva colección llamada "assets"
2. Configura los siguientes atributos:
   - `name` (String, 200, Required)
   - `code` (String, 50, Required, Unique)
   - `type` (String, 50, Required)
   - `description` (String, 1000, Optional)
   - `location` (String, 200, Optional)
   - `status` (String, 50, Required, Default: "Activo")
   - `brand` (String, 100, Optional)
   - `model` (String, 100, Optional)
   - `serial_number` (String, 100, Optional)
   - `purchase_date` (DateTime, Optional)
   - `purchase_price` (Float, Optional)
   - `image_url` (String, 500, Optional)
   - `qr_code` (String, 200, Optional)
   - `custom_fields` (String, 2000, Optional) // JSON string
   - `created_at` (DateTime, Required)
   - `updated_at` (DateTime, Optional)
   - `created_by` (String, 50, Required) // User ID

3. Configurar permisos:
   - Read: `users`
   - Create: `users`
   - Update: `users`
   - Delete: `admins`

#### Colección: reports
1. Crea una nueva colección llamada "reports"
2. Configura los siguientes atributos:
   - `asset_id` (String, 50, Required) // Asset ID
   - `title` (String, 200, Required)
   - `description` (String, 2000, Required)
   - `priority` (String, 50, Required, Default: "Media")
   - `status` (String, 50, Required, Default: "Pendiente")
   - `image_urls` (String, 2000, Optional) // JSON array string
   - `location` (String, 200, Optional)
   - `reported_at` (DateTime, Required)
   - `reported_by` (String, 50, Required) // User ID
   - `assigned_to` (String, 50, Optional) // User ID
   - `assigned_at` (DateTime, Optional)
   - `resolved_at` (DateTime, Optional)
   - `resolution` (String, 2000, Optional)
   - `metadata` (String, 2000, Optional) // JSON string

3. Configurar permisos:
   - Read: `users`
   - Create: `users`
   - Update: `users`
   - Delete: `admins`

#### Colección: work_orders
1. Crea una nueva colección llamada "work_orders"
2. Configura los siguientes atributos:
   - `report_id` (String, 50, Optional) // Report ID
   - `asset_id` (String, 50, Required) // Asset ID
   - `title` (String, 200, Required)
   - `description` (String, 2000, Required)
   - `type` (String, 50, Required)
   - `priority` (String, 50, Required, Default: "Media")
   - `status` (String, 50, Required, Default: "Pendiente")
   - `assigned_to` (String, 50, Optional) // User ID
   - `scheduled_date` (DateTime, Optional)
   - `started_at` (DateTime, Optional)
   - `completed_at` (DateTime, Optional)
   - `estimated_hours` (Integer, Optional)
   - `actual_hours` (Integer, Optional)
   - `materials` (String, 2000, Optional) // JSON array string
   - `tasks` (String, 2000, Optional) // JSON array string
   - `notes` (String, 2000, Optional)
   - `image_urls` (String, 2000, Optional) // JSON array string
   - `signature_url` (String, 500, Optional)
   - `created_at` (DateTime, Required)
   - `created_by` (String, 50, Required) // User ID
   - `updated_at` (DateTime, Optional)

3. Configurar permisos:
   - Read: `users`
   - Create: `users`
   - Update: `users`
   - Delete: `admins`

## 4. Configurar Storage

1. Ve a la sección "Storage" en Appwrite
2. Crea los siguientes buckets:

### Bucket: asset-images
- Name: "asset-images"
- File Security: Enabled
- Maximum File Size: 10MB
- Allowed File Extensions: jpg, jpeg, png, webp
- Permissions:
  - Read: `users`
  - Create: `users`
  - Update: `users`
  - Delete: `admins`

### Bucket: documents
- Name: "documents"
- File Security: Enabled
- Maximum File Size: 50MB
- Allowed File Extensions: pdf, doc, docx, xls, xlsx
- Permissions:
  - Read: `users`
  - Create: `users`
  - Update: `users`
  - Delete: `admins`

## 5. Configurar Funciones (Opcional)

Puedes crear funciones serverless para:
- Envío de notificaciones automáticas
- Generación de reportes PDF
- Procesamiento de imágenes
- Validaciones complejas

## 6. Próximos Pasos

Una vez configurado Appwrite:

1. **Actualiza las constantes**: Modifica `lib/core/constants/app_constants.dart` con tus credenciales reales
2. **Ejecuta la aplicación**: `flutter run` para probar la conexión
3. **Crea usuarios de prueba**: Registra algunos usuarios con diferentes roles
4. **Prueba la funcionalidad**: Crea activos, reportes y órdenes de trabajo
5. **Configura notificaciones**: Implementa push notifications si es necesario

## 7. Comandos Útiles

```bash
# Instalar dependencias
flutter pub get

# Ejecutar análisis de código
flutter analyze

# Ejecutar en modo debug
flutter run

# Limpiar proyecto
flutter clean
```

## 8. Troubleshooting

### Error de conexión
- Verifica que el endpoint y project ID sean correctos
- Asegúrate de que el proyecto esté activo en Appwrite
- Revisa la configuración de CORS si usas web

### Errores de permisos
- Verifica que los permisos de las colecciones estén configurados correctamente
- Asegúrate de que el usuario esté autenticado
- Revisa los roles y permisos en la consola de Appwrite

### Problemas con Storage
- Verifica que los buckets existan y tengan los permisos correctos
- Asegúrate de que los tipos de archivo estén permitidos
- Revisa el tamaño máximo de archivo configurado
