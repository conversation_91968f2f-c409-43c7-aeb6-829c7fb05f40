import 'package:flutter/foundation.dart';

/// Servicio de notificaciones simplificado (sin dependencias externas)
class NotificationService {
  static bool _initialized = false;
  static bool _notificationsEnabled = true;

  /// Inicializa el servicio de notificaciones
  static Future<void> initialize() async {
    if (_initialized) return;

    _initialized = true;
    debugPrint('🔔 NotificationService initialized (mock mode)');
  }

  /// Habilita o deshabilita las notificaciones
  static void setNotificationsEnabled(bool enabled) {
    _notificationsEnabled = enabled;
    debugPrint('🔔 Notifications ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Verifica si las notificaciones están habilitadas
  static bool get isEnabled => _notificationsEnabled;

  /// Muestra una notificación inmediata (simulada)
  static Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_notificationsEnabled) return;
    
    debugPrint('🔔 Notification: $title - $body');
  }

  /// Programa una notificación para más tarde (simulada)
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    if (!_notificationsEnabled) return;
    
    debugPrint('🔔 Scheduled notification: $title for $scheduledDate');
  }

  /// Cancela una notificación específica (simulada)
  static Future<void> cancelNotification(int id) async {
    debugPrint('🔔 Cancelled notification: $id');
  }

  /// Cancela todas las notificaciones (simulada)
  static Future<void> cancelAllNotifications() async {
    debugPrint('🔔 Cancelled all notifications');
  }

  /// Métodos específicos para la aplicación
  static Future<void> notifyReportAssigned({
    required String reportTitle,
    required String assignedTo,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: '📋 Reporte Asignado',
      body: '$reportTitle asignado a $assignedTo',
      payload: 'report_assigned',
    );
  }

  static Future<void> notifyScheduledMaintenance({
    required String assetName,
    required DateTime scheduledDate,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: '🔧 Mantenimiento Programado',
      body: 'Mantenimiento de $assetName programado para ${scheduledDate.day}/${scheduledDate.month}',
      payload: 'maintenance_scheduled',
    );
  }

  static Future<void> notifyOverdueReport({
    required String reportTitle,
    required int daysOverdue,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: '⚠️ Reporte Vencido',
      body: '$reportTitle lleva $daysOverdue días vencido',
      payload: 'report_overdue',
    );
  }
}
