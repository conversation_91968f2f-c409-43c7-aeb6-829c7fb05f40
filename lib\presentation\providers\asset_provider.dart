import 'package:flutter/foundation.dart';
import '../../data/models/asset_model.dart';
import '../../core/services/backend_service.dart';
import '../../core/constants/app_constants.dart';

class AssetProvider with ChangeNotifier {
  final BackendService _backend = BackendService.instance;

  List<AssetModel> _assets = [];
  AssetModel? _selectedAsset;
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  String? _typeFilter;
  String? _statusFilter;

  List<AssetModel> get assets => _assets;
  AssetModel? get selectedAsset => _selectedAsset;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  String? get typeFilter => _typeFilter;
  String? get statusFilter => _statusFilter;

  List<AssetModel> get filteredAssets {
    List<AssetModel> filtered = _assets;

    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where(
            (asset) =>
                asset.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                asset.code.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                asset.type.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                (asset.description?.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ??
                    false),
          )
          .toList();
    }

    if (_typeFilter != null && _typeFilter!.isNotEmpty) {
      filtered = filtered.where((asset) => asset.type == _typeFilter).toList();
    }

    if (_statusFilter != null && _statusFilter!.isNotEmpty) {
      filtered = filtered
          .where((asset) => asset.status == _statusFilter)
          .toList();
    }

    return filtered;
  }

  Future<void> loadAssets() async {
    _setLoading(true);
    _clearError();

    try {
      // Simular datos mock para desarrollo
      await Future.delayed(const Duration(milliseconds: 500));

      _assets = _generateMockAssets();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Error al cargar activos: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<bool> createAsset(AssetModel asset) async {
    _setLoading(true);
    _clearError();

    try {
      // Simular creación
      await Future.delayed(const Duration(milliseconds: 500));

      _assets.insert(0, asset);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al crear activo');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateAsset(AssetModel asset) async {
    _setLoading(true);
    _clearError();

    try {
      // Simular actualización
      await Future.delayed(const Duration(milliseconds: 500));

      final index = _assets.indexWhere((a) => a.id == asset.id);
      if (index != -1) {
        _assets[index] = asset;
      }

      if (_selectedAsset?.id == asset.id) {
        _selectedAsset = asset;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al actualizar activo');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> deleteAsset(String assetId) async {
    _setLoading(true);
    _clearError();

    try {
      // Simular eliminación
      await Future.delayed(const Duration(milliseconds: 500));

      _assets.removeWhere((asset) => asset.id == assetId);

      if (_selectedAsset?.id == assetId) {
        _selectedAsset = null;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al eliminar activo');
      _setLoading(false);
      return false;
    }
  }

  Future<void> loadAssetById(String assetId) async {
    _setLoading(true);
    _clearError();

    try {
      // Buscar en la lista local
      await Future.delayed(const Duration(milliseconds: 300));

      _selectedAsset = _assets.firstWhere(
        (asset) => asset.id == assetId,
        orElse: () => throw Exception('Activo no encontrado'),
      );

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Error al cargar activo');
      _setLoading(false);
    }
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void searchAssets(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void filterAssets({String? type, String? status}) {
    _typeFilter = type;
    _statusFilter = status;
    notifyListeners();
  }

  void setTypeFilter(String type) {
    _typeFilter = type;
    notifyListeners();
  }

  void setStatusFilter(String status) {
    _statusFilter = status;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _typeFilter = null;
    _statusFilter = null;
    notifyListeners();
  }

  void selectAsset(AssetModel asset) {
    _selectedAsset = asset;
    notifyListeners();
  }

  void clearSelectedAsset() {
    _selectedAsset = null;
    notifyListeners();
  }

  Future<String> generateAssetCode(String type) async {
    try {
      final prefix = type.substring(0, 3).toUpperCase();
      final existingCodes = _assets
          .where((asset) => asset.code.startsWith(prefix))
          .map((asset) => asset.code)
          .toList();

      if (existingCodes.isEmpty) {
        return '${prefix}001';
      }

      final numbers = existingCodes
          .map((code) => int.tryParse(code.substring(3)) ?? 0)
          .toList();

      final maxNumber = numbers.reduce((a, b) => a > b ? a : b);
      return '$prefix${(maxNumber + 1).toString().padLeft(3, '0')}';
    } catch (e) {
      return '${type.substring(0, 3).toUpperCase()}001';
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Generar datos mock
  List<AssetModel> _generateMockAssets() {
    final now = DateTime.now();
    return [
      AssetModel(
        id: 'asset_001',
        name: 'Computadora Dell Optiplex',
        code: 'COMP-001',
        type: 'Tecnología',
        status: 'Operativo',
        createdAt: now.subtract(const Duration(days: 30)),
        createdBy: 'Admin Sistema',
        description:
            'Computadora de escritorio Dell Optiplex 7090 con procesador Intel Core i7, 16GB RAM, SSD 512GB.',
        location: 'Oficina Principal - Piso 2 - Escritorio 15',
        brand: 'Dell',
        model: 'Optiplex 7090',
        serialNumber: 'DL7090-2024-001',
        purchaseDate: now.subtract(const Duration(days: 365)),
        purchasePrice: 1200.00,
        imageUrl: '/mock/images/dell_optiplex.jpg',
      ),

      AssetModel(
        id: 'asset_002',
        name: 'Impresora HP LaserJet',
        code: 'PRINT-002',
        type: 'Equipo',
        status: 'Mantenimiento',
        createdAt: now.subtract(const Duration(days: 45)),
        createdBy: 'Admin Sistema',
        description:
            'Impresora láser HP LaserJet Pro 4025dn monocromática con capacidad de impresión dúplex automática.',
        location: 'Área de Administración - Estación de Impresión',
        brand: 'HP',
        model: 'LaserJet Pro 4025dn',
        serialNumber: 'HP4025-2023-002',
        purchaseDate: now.subtract(const Duration(days: 200)),
        purchasePrice: 450.00,
        imageUrl: '/mock/images/hp_laserjet.jpg',
      ),

      AssetModel(
        id: 'asset_003',
        name: 'Aire Acondicionado Central',
        code: 'HVAC-003',
        type: 'Infraestructura',
        status: 'Operativo',
        createdAt: now.subtract(const Duration(days: 180)),
        createdBy: 'Admin Sistema',
        description:
            'Sistema de aire acondicionado central marca Carrier de 5 toneladas con tecnología inverter.',
        location: 'Edificio Principal - Azotea - Unidad Central',
        brand: 'Carrier',
        model: '40RUQA060',
        serialNumber: 'CR5T-2023-003',
        purchaseDate: now.subtract(const Duration(days: 400)),
        purchasePrice: 8500.00,
        imageUrl: '/mock/images/carrier_ac.jpg',
      ),

      AssetModel(
        id: 'asset_004',
        name: 'Vehículo Toyota Hilux',
        code: 'VEH-004',
        type: 'Vehículo',
        status: 'En Reparación',
        createdAt: now.subtract(const Duration(days: 90)),
        createdBy: 'Admin Sistema',
        description:
            'Camioneta Toyota Hilux 4x4 modelo 2022, motor 2.8L turbo diésel.',
        location: 'Estacionamiento Principal - Bahía 3',
        brand: 'Toyota',
        model: 'Hilux 4x4 SR',
        serialNumber: 'TH4X4-2022-004',
        purchaseDate: now.subtract(const Duration(days: 600)),
        purchasePrice: 35000.00,
        imageUrl: '/mock/images/toyota_hilux.jpg',
      ),

      AssetModel(
        id: 'asset_005',
        name: 'Servidor Principal',
        code: 'SRV-005',
        type: 'Tecnología',
        status: 'Operativo',
        createdAt: now.subtract(const Duration(days: 120)),
        createdBy: 'Admin Sistema',
        description:
            'Servidor Dell PowerEdge R750 con procesadores Intel Xeon, 64GB RAM, almacenamiento SSD en RAID.',
        location: 'Sala de Servidores - Rack A1',
        brand: 'Dell',
        model: 'PowerEdge R750',
        serialNumber: 'DPE750-2023-005',
        purchaseDate: now.subtract(const Duration(days: 300)),
        purchasePrice: 12000.00,
        imageUrl: '/mock/images/dell_server.jpg',
      ),

      AssetModel(
        id: 'asset_006',
        name: 'Silla de Oficina Ergonómica',
        code: 'FURN-006',
        type: 'Mobiliario',
        status: 'Operativo',
        createdAt: now.subtract(const Duration(days: 60)),
        createdBy: 'Admin Sistema',
        description:
            'Silla ergonómica Herman Miller Aeron talla B con soporte lumbar ajustable.',
        location: 'Oficina Principal - Piso 1 - Escritorio 8',
        brand: 'Herman Miller',
        model: 'Aeron Talla B',
        serialNumber: 'HM-AER-B-006',
        purchaseDate: now.subtract(const Duration(days: 150)),
        purchasePrice: 1400.00,
        imageUrl: '/mock/images/herman_miller_aeron.jpg',
      ),

      AssetModel(
        id: 'asset_007',
        name: 'Lámpara LED Escritorio',
        code: 'LIGHT-007',
        type: 'Mobiliario',
        status: 'Operativo',
        createdAt: now.subtract(const Duration(days: 15)),
        createdBy: 'Admin Sistema',
        description:
            'Lámpara LED de escritorio Philips con brazo articulado, control táctil de intensidad.',
        location: 'Área de Contabilidad - Escritorio Principal',
        brand: 'Philips',
        model: 'EyeCare LED',
        serialNumber: 'PH-LED-EC-007',
        purchaseDate: now.subtract(const Duration(days: 30)),
        purchasePrice: 85.00,
        imageUrl: '/mock/images/philips_led_lamp.jpg',
      ),
    ];
  }
}
