import 'package:flutter/foundation.dart';
import '../../data/models/asset_model.dart';
import '../../core/services/backend_service.dart';
import '../../core/constants/app_constants.dart';
// import '../../core/errors/failures.dart';

class AssetProvider extends ChangeNotifier {
  final BackendService _backend = BackendService.instance;

  List<AssetModel> _assets = [];
  List<AssetModel> _filteredAssets = [];
  AssetModel? _selectedAsset;
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  String? _typeFilter;
  String? _statusFilter;

  List<AssetModel> get assets => _assets;
  AssetModel? get selectedAsset => _selectedAsset;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  String? get typeFilter => _typeFilter;
  String? get statusFilter => _statusFilter;

  List<AssetModel> get filteredAssets {
    var filtered = _assets;

    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where(
            (asset) =>
                asset.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                asset.code.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                (asset.description?.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ??
                    false),
          )
          .toList();
    }

    if (_typeFilter?.isNotEmpty == true) {
      filtered = filtered.where((asset) => asset.type == _typeFilter).toList();
    }

    if (_statusFilter?.isNotEmpty == true) {
      filtered = filtered
          .where((asset) => asset.status == _statusFilter)
          .toList();
    }

    return filtered;
  }

  Future<void> loadAssets() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _backend.databases.listDocuments(
        databaseId: AppConstants.appwriteDatabaseId,
        collectionId: AppConstants.assetsCollectionId,
        queries: ['orderDesc:created_at'],
      );

      _assets = response.documents
          .map((doc) => AssetModel.fromJson(doc.data))
          .toList();

      _setLoading(false);
    } catch (e) {
      _setError('Error al cargar activos');
      _setLoading(false);
    }
  }

  Future<bool> createAsset(AssetModel asset) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _backend.databases.createDocument(
        databaseId: AppConstants.appwriteDatabaseId,
        collectionId: AppConstants.assetsCollectionId,
        documentId: 'asset_${DateTime.now().millisecondsSinceEpoch}',
        data: asset.toJson(),
      );

      final newAsset = AssetModel.fromJson(response.data);
      _assets.insert(0, newAsset);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al crear activo');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateAsset(AssetModel asset) async {
    _setLoading(true);
    _clearError();

    try {
      await _backend.databases.updateDocument(
        databaseId: AppConstants.appwriteDatabaseId,
        collectionId: AppConstants.assetsCollectionId,
        documentId: asset.id,
        data: asset.toJson(),
      );

      final index = _assets.indexWhere((a) => a.id == asset.id);
      if (index != -1) {
        _assets[index] = asset;
      }

      if (_selectedAsset?.id == asset.id) {
        _selectedAsset = asset;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al actualizar activo');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> deleteAsset(String assetId) async {
    _setLoading(true);
    _clearError();

    try {
      await _backend.databases.deleteDocument(
        databaseId: AppConstants.appwriteDatabaseId,
        collectionId: AppConstants.assetsCollectionId,
        documentId: assetId,
      );

      _assets.removeWhere((asset) => asset.id == assetId);

      if (_selectedAsset?.id == assetId) {
        _selectedAsset = null;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al eliminar activo');
      _setLoading(false);
      return false;
    }
  }

  Future<void> loadAssetById(String assetId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _backend.databases.getDocument(
        databaseId: AppConstants.appwriteDatabaseId,
        collectionId: AppConstants.assetsCollectionId,
        documentId: assetId,
      );

      _selectedAsset = AssetModel.fromJson(response.data);
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Error al cargar activo');
      _setLoading(false);
    }
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  // Métodos de búsqueda y filtrado
  void searchAssets(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void filterAssets({String? type, String? status}) {
    _typeFilter = type;
    _statusFilter = status;
    notifyListeners();
  }

  void setTypeFilter(String type) {
    _typeFilter = type;
    notifyListeners();
  }

  void setStatusFilter(String status) {
    _statusFilter = status;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _typeFilter = null;
    _statusFilter = null;
    notifyListeners();
  }

  void selectAsset(AssetModel asset) {
    _selectedAsset = asset;
    notifyListeners();
  }

  void clearSelectedAsset() {
    _selectedAsset = null;
    notifyListeners();
  }

  Future<String> generateAssetCode(String type) async {
    try {
      final prefix = type.substring(0, 3).toUpperCase();
      final response = await _backend.databases.listDocuments(
        databaseId: AppConstants.appwriteDatabaseId,
        collectionId: AppConstants.assetsCollectionId,
        queries: ['startsWith:code:$prefix', 'orderDesc:code', 'limit:1'],
      );

      if (response.documents.isEmpty) {
        return '${prefix}001';
      }

      final lastCode = response.documents.first.data['code'] as String;
      final number = int.parse(lastCode.substring(3)) + 1;
      return '$prefix${number.toString().padLeft(3, '0')}';
    } catch (e) {
      return '${type.substring(0, 3).toUpperCase()}001';
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
