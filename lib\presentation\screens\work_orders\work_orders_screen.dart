import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../data/models/maintenance_model.dart';
import '../../providers/maintenance_provider.dart';
import 'create_work_order_screen.dart';

class WorkOrdersScreen extends StatefulWidget {
  const WorkOrdersScreen({super.key});

  @override
  State<WorkOrdersScreen> createState() => _WorkOrdersScreenState();
}

class _WorkOrdersScreenState extends State<WorkOrdersScreen> {
  final _searchController = TextEditingController();
  String _selectedStatusFilter = '';
  String _selectedPriorityFilter = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<MaintenanceProvider>(
        context,
        listen: false,
      ).loadMaintenances();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Órdenes de Trabajo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Consumer<MaintenanceProvider>(
        builder: (context, maintenanceProvider, _) {
          if (maintenanceProvider.isLoading) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'Cargando órdenes de trabajo...',
                    style: TextStyle(color: AppColors.textSecondary),
                  ),
                ],
              ),
            );
          }

          // Aplicar filtros
          final filteredMaintenances = maintenanceProvider.maintenances.where((
            maintenance,
          ) {
            final searchQuery = _searchController.text.toLowerCase();
            final matchesSearch =
                searchQuery.isEmpty ||
                maintenance.title.toLowerCase().contains(searchQuery) ||
                maintenance.description.toLowerCase().contains(searchQuery) ||
                maintenance.assetName.toLowerCase().contains(searchQuery) ||
                (maintenance.assignedTo?.toLowerCase().contains(searchQuery) ??
                    false);

            final matchesStatus =
                _selectedStatusFilter.isEmpty ||
                maintenance.status == _selectedStatusFilter;

            final matchesPriority =
                _selectedPriorityFilter.isEmpty ||
                maintenance.priority == _selectedPriorityFilter;

            return matchesSearch && matchesStatus && matchesPriority;
          }).toList();

          final groupedByTechnician = _groupMaintenancesByTechnician(
            filteredMaintenances,
          );

          if (groupedByTechnician.isEmpty) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              // Barra de búsqueda
              _buildSearchBar(),

              // Lista de técnicos con sus órdenes
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () => maintenanceProvider.loadMaintenances(),
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: groupedByTechnician.keys.length,
                    itemBuilder: (context, index) {
                      final technician = groupedByTechnician.keys.elementAt(
                        index,
                      );
                      final techMaintenances = groupedByTechnician[technician]!;
                      return _buildTechnicianCard(technician, techMaintenances);
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createWorkOrder,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add_task),
        label: const Text('Nueva Orden'),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Buscar por técnico o activo...',
          prefixIcon: const Icon(Icons.search, color: AppColors.primary),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppColors.border),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppColors.primary, width: 2),
          ),
          filled: true,
          fillColor: AppColors.background,
        ),
        onChanged: _updateSearch,
      ),
    );
  }

  Widget _buildTechnicianCard(
    String technician,
    List<MaintenanceModel> maintenances,
  ) {
    final pendingCount = maintenances
        .where((m) => m.status == 'Pendiente')
        .length;
    final inProgressCount = maintenances
        .where((m) => m.status == 'En Progreso')
        .length;
    final completedCount = maintenances
        .where((m) => m.status == 'Completado')
        .length;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary,
          child: Text(
            technician.isNotEmpty ? technician[0].toUpperCase() : 'T',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          technician.isEmpty ? 'Sin Asignar' : technician,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${maintenances.length} órdenes asignadas',
              style: const TextStyle(color: AppColors.textSecondary),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildStatusChip('Pendiente', pendingCount, AppColors.warning),
                const SizedBox(width: 8),
                _buildStatusChip(
                  'En Progreso',
                  inProgressCount,
                  AppColors.info,
                ),
                const SizedBox(width: 8),
                _buildStatusChip(
                  'Completado',
                  completedCount,
                  AppColors.success,
                ),
              ],
            ),
          ],
        ),
        children: maintenances
            .map((maintenance) => _buildMaintenanceItem(maintenance))
            .toList(),
      ),
    );
  }

  Widget _buildStatusChip(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        '$count $label',
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildMaintenanceItem(MaintenanceModel maintenance) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  maintenance.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(maintenance.status),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  maintenance.status,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Activo: ${maintenance.assetName}',
            style: const TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
          if (maintenance.description.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              maintenance.description,
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.schedule, size: 14, color: AppColors.textSecondary),
              const SizedBox(width: 4),
              Text(
                'Programado: ${_formatDate(maintenance.scheduledDate)}',
                style: const TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 11,
                ),
              ),
              const Spacer(),
              if (maintenance.priority.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getPriorityColor(
                      maintenance.priority,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getPriorityColor(
                        maintenance.priority,
                      ).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    maintenance.priority,
                    style: TextStyle(
                      color: _getPriorityColor(maintenance.priority),
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.assignment, size: 64, color: AppColors.textSecondary),
          SizedBox(height: 16),
          Text(
            'No hay órdenes de trabajo',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Las órdenes de trabajo aparecerán aquí',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Map<String, List<MaintenanceModel>> _groupMaintenancesByTechnician(
    List<MaintenanceModel> maintenances,
  ) {
    final Map<String, List<MaintenanceModel>> grouped = {};

    for (final maintenance in maintenances) {
      final technician = maintenance.assignedTo ?? 'Sin Asignar';
      if (!grouped.containsKey(technician)) {
        grouped[technician] = [];
      }
      grouped[technician]!.add(maintenance);
    }

    return grouped;
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pendiente':
        return AppColors.warning;
      case 'en progreso':
        return AppColors.info;
      case 'completado':
        return AppColors.success;
      case 'cancelado':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.black;
      case 'alta':
        return AppColors.error;
      case 'media':
        return AppColors.info;
      case 'baja':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void _updateSearch(String query) {
    setState(() {
      // La búsqueda se maneja en el Consumer con filtros
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrar Órdenes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: _selectedStatusFilter.isEmpty
                  ? null
                  : _selectedStatusFilter,
              decoration: const InputDecoration(
                labelText: 'Estado',
                border: OutlineInputBorder(),
              ),
              items: ['', 'Pendiente', 'En Progreso', 'Completado', 'Cancelado']
                  .map(
                    (status) => DropdownMenuItem(
                      value: status,
                      child: Text(status.isEmpty ? 'Todos' : status),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatusFilter = value ?? '';
                });
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedPriorityFilter.isEmpty
                  ? null
                  : _selectedPriorityFilter,
              decoration: const InputDecoration(
                labelText: 'Prioridad',
                border: OutlineInputBorder(),
              ),
              items: ['', 'Baja', 'Media', 'Alta', 'Crítica']
                  .map(
                    (priority) => DropdownMenuItem(
                      value: priority,
                      child: Text(priority.isEmpty ? 'Todas' : priority),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPriorityFilter = value ?? '';
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {}); // Actualizar la vista con filtros
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary),
            child: const Text('Aplicar', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _createWorkOrder() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CreateWorkOrderScreen()),
    );
  }
}
