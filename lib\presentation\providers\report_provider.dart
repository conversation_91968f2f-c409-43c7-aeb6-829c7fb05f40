import 'package:flutter/foundation.dart';
import '../../data/models/report_model.dart';
import '../../core/services/backend_service.dart';
import '../../core/constants/app_constants.dart';

class ReportProvider with ChangeNotifier {
  final BackendService _backend = BackendService.instance;

  List<ReportModel> _reports = [];
  List<ReportModel> _filteredReports = [];
  ReportModel? _selectedReport;
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  String? _typeFilter;
  String? _statusFilter;
  String? _priorityFilter;

  // Getters
  List<ReportModel> get reports => _reports;
  List<ReportModel> get filteredReports {
    var filtered = List<ReportModel>.from(_reports);

    // Aplicar búsqueda
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((report) {
        return report.title.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            report.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            report.assetName.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Aplicar filtros
    if (_typeFilter?.isNotEmpty == true) {
      filtered = filtered
          .where((report) => report.type == _typeFilter)
          .toList();
    }

    if (_statusFilter?.isNotEmpty == true) {
      filtered = filtered
          .where((report) => report.status == _statusFilter)
          .toList();
    }

    if (_priorityFilter?.isNotEmpty == true) {
      filtered = filtered
          .where((report) => report.priority == _priorityFilter)
          .toList();
    }

    return filtered;
  }

  ReportModel? get selectedReport => _selectedReport;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  String? get typeFilter => _typeFilter;
  String? get statusFilter => _statusFilter;
  String? get priorityFilter => _priorityFilter;

  // Métodos de búsqueda y filtrado
  void searchReports(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void filterReports({String? type, String? status, String? priority}) {
    _typeFilter = type;
    _statusFilter = status;
    _priorityFilter = priority;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _typeFilter = null;
    _statusFilter = null;
    _priorityFilter = null;
    notifyListeners();
  }

  // Métodos CRUD
  Future<void> loadReports() async {
    _setLoading(true);
    _clearError();

    try {
      // Simular datos mock para desarrollo
      await Future.delayed(const Duration(milliseconds: 500));

      _reports = _generateMockReports();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Error al cargar reportes: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<bool> createReport(ReportModel report) async {
    _setLoading(true);
    _clearError();

    try {
      // Simular creación
      await Future.delayed(const Duration(milliseconds: 800));

      final newReport = report.copyWith(
        id: 'report_${DateTime.now().millisecondsSinceEpoch}',
      );

      _reports.insert(0, newReport);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al crear reporte: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateReport(ReportModel report) async {
    _setLoading(true);
    _clearError();

    try {
      // Simular actualización
      await Future.delayed(const Duration(milliseconds: 600));

      final index = _reports.indexWhere((r) => r.id == report.id);
      if (index != -1) {
        _reports[index] = report;
        if (_selectedReport?.id == report.id) {
          _selectedReport = report;
        }
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al actualizar reporte: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> deleteReport(String reportId) async {
    _setLoading(true);
    _clearError();

    try {
      // Simular eliminación
      await Future.delayed(const Duration(milliseconds: 400));

      _reports.removeWhere((report) => report.id == reportId);
      if (_selectedReport?.id == reportId) {
        _selectedReport = null;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al eliminar reporte: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<ReportModel?> getReportById(String reportId) async {
    try {
      return _reports.firstWhere((report) => report.id == reportId);
    } catch (e) {
      return null;
    }
  }

  void selectReport(ReportModel? report) {
    _selectedReport = report;
    notifyListeners();
  }

  // Métodos de utilidad
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Generar datos mock
  List<ReportModel> _generateMockReports() {
    final now = DateTime.now();
    return [
      ReportModel(
        id: 'report_001',
        assetId: 'asset_001',
        assetName: 'Computadora Dell Optiplex',
        title: 'Pantalla no enciende',
        description:
            'La pantalla del monitor no enciende al presionar el botón de encendido. Se verificó que el cable de alimentación está conectado correctamente.',
        priority: 'Alta',
        status: 'Abierto',
        type: 'Falla',
        reportedAt: now.subtract(const Duration(hours: 2)),
        reportedBy: 'Juan Pérez',
        assignedTo: 'Ana Técnico IT',
        assignedAt: now.subtract(const Duration(hours: 1)),
        location: 'Oficina Principal - Piso 2',
      ),
      ReportModel(
        id: 'report_002',
        assetId: 'asset_002',
        assetName: 'Impresora HP LaserJet',
        title: 'Atasco de papel frecuente',
        description:
            'La impresora presenta atascos de papel de manera frecuente, especialmente al imprimir documentos de varias páginas.',
        priority: 'Media',
        status: 'En Progreso',
        type: 'Falla',
        reportedAt: now.subtract(const Duration(days: 1)),
        reportedBy: 'María García',
        assignedTo: 'Carlos Técnico',
        assignedAt: now.subtract(const Duration(hours: 8)),
        location: 'Área de Administración',
      ),
      ReportModel(
        id: 'report_003',
        assetId: 'asset_003',
        assetName: 'Aire Acondicionado Central',
        title: 'Mantenimiento preventivo mensual',
        description:
            'Realizar mantenimiento preventivo mensual: limpieza de filtros, revisión de refrigerante y verificación de componentes.',
        priority: 'Media',
        status: 'Resuelto',
        type: 'Mantenimiento',
        reportedAt: now.subtract(const Duration(days: 3)),
        reportedBy: 'Sistema Automático',
        assignedTo: 'Pedro Mantenimiento',
        assignedAt: now.subtract(const Duration(days: 2)),
        resolvedAt: now.subtract(const Duration(hours: 4)),
        resolution:
            'Mantenimiento completado exitosamente. Filtros limpiados y refrigerante verificado.',
        location: 'Edificio Principal',
      ),
      ReportModel(
        id: 'report_004',
        assetId: 'asset_004',
        assetName: 'Vehículo Toyota Hilux',
        title: 'Ruido extraño en el motor',
        description:
            'Se escucha un ruido metálico extraño proveniente del motor, especialmente al acelerar.',
        priority: 'Crítica',
        status: 'En Revisión',
        type: 'Falla',
        reportedAt: now.subtract(const Duration(hours: 6)),
        reportedBy: 'Luis Conductor',
        assignedTo: 'Taller Mecánico',
        assignedAt: now.subtract(const Duration(hours: 4)),
        location: 'Estacionamiento Principal',
      ),
      ReportModel(
        id: 'report_005',
        assetId: 'asset_005',
        assetName: 'Servidor Principal',
        title: 'Inspección de seguridad trimestral',
        description:
            'Inspección trimestral de seguridad del servidor: verificación de logs, actualización de parches y revisión de configuraciones.',
        priority: 'Alta',
        status: 'Abierto',
        type: 'Inspección',
        reportedAt: now.subtract(const Duration(hours: 12)),
        reportedBy: 'Sistema de Monitoreo',
        assignedTo: 'Roberto Seguridad',
        assignedAt: now.subtract(const Duration(hours: 8)),
        location: 'Sala de Servidores',
      ),
      ReportModel(
        id: 'report_006',
        assetId: 'asset_006',
        assetName: 'Silla de Oficina Ergonómica',
        title: 'Ajuste de altura no funciona correctamente',
        description:
            'El mecanismo de ajuste de altura de la silla presenta dificultad para subir y bajar. No es urgente pero requiere revisión.',
        priority: 'Baja',
        status: 'Abierto',
        type: 'Falla',
        reportedAt: now.subtract(const Duration(days: 2)),
        reportedBy: 'Sandra Oficina',
        assignedTo: 'Miguel Mobiliario',
        assignedAt: now.subtract(const Duration(days: 1)),
        location: 'Oficina Principal - Piso 1',
      ),
      ReportModel(
        id: 'report_007',
        assetId: 'asset_007',
        assetName: 'Lámpara LED Escritorio',
        title: 'Cambio de lámpara fundida',
        description:
            'Reemplazo de lámpara LED que se fundió en el escritorio del área de contabilidad. Trabajo completado y verificado.',
        priority: 'Baja',
        status: 'Cerrado',
        type: 'Mantenimiento',
        reportedAt: now.subtract(const Duration(days: 5)),
        reportedBy: 'Ana Contabilidad',
        assignedTo: 'Luis Electricista',
        assignedAt: now.subtract(const Duration(days: 4)),
        resolvedAt: now.subtract(const Duration(days: 1)),
        resolution:
            'Lámpara LED reemplazada exitosamente. Funcionamiento verificado y aprobado por el usuario.',
        location: 'Área de Contabilidad',
      ),
    ];
  }

  // Métodos de estadísticas
  Map<String, int> getReportsByStatus() {
    final statusCount = <String, int>{};
    for (final status in AppConstants.reportStatuses) {
      statusCount[status] = _reports.where((r) => r.status == status).length;
    }
    return statusCount;
  }

  Map<String, int> getReportsByPriority() {
    final priorityCount = <String, int>{};
    for (final priority in AppConstants.reportPriorities) {
      priorityCount[priority] = _reports
          .where((r) => r.priority == priority)
          .length;
    }
    return priorityCount;
  }

  Map<String, int> getReportsByType() {
    final typeCount = <String, int>{};
    for (final type in AppConstants.reportTypes) {
      typeCount[type] = _reports.where((r) => r.type == type).length;
    }
    return typeCount;
  }

  List<ReportModel> getRecentReports({int limit = 5}) {
    final sortedReports = List<ReportModel>.from(_reports);
    sortedReports.sort((a, b) => b.reportedAt.compareTo(a.reportedAt));
    return sortedReports.take(limit).toList();
  }

  List<ReportModel> getHighPriorityReports() {
    return _reports
        .where((r) => r.priority == 'Crítica' || r.priority == 'Alta')
        .toList();
  }

  List<ReportModel> getOpenReports() {
    return _reports
        .where((r) => r.status == 'Abierto' || r.status == 'En Progreso')
        .toList();
  }
}
