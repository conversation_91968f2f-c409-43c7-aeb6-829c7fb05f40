import 'package:flutter/foundation.dart';
import '../../data/models/report_model.dart';
import '../../core/services/backend_service.dart';
import '../../core/constants/app_constants.dart';

class ReportProvider with ChangeNotifier {
  final BackendService _backend = BackendService.instance;

  List<ReportModel> _reports = [];
  final List<ReportModel> _filteredReports = [];
  ReportModel? _selectedReport;
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  String? _typeFilter;
  String? _statusFilter;
  String? _priorityFilter;

  // Getters
  List<ReportModel> get reports => _reports;
  List<ReportModel> get filteredReports {
    var filtered = List<ReportModel>.from(_reports);

    // Aplicar búsqueda
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((report) {
        return report.title.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            report.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            report.assetName.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Aplicar filtros
    if (_typeFilter?.isNotEmpty == true) {
      filtered = filtered
          .where((report) => report.type == _typeFilter)
          .toList();
    }

    if (_statusFilter?.isNotEmpty == true) {
      filtered = filtered
          .where((report) => report.status == _statusFilter)
          .toList();
    }

    if (_priorityFilter?.isNotEmpty == true) {
      filtered = filtered
          .where((report) => report.priority == _priorityFilter)
          .toList();
    }

    return filtered;
  }

  ReportModel? get selectedReport => _selectedReport;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  String? get typeFilter => _typeFilter;
  String? get statusFilter => _statusFilter;
  String? get priorityFilter => _priorityFilter;

  // Métodos de búsqueda y filtrado
  void searchReports(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void filterReports({String? type, String? status, String? priority}) {
    _typeFilter = type;
    _statusFilter = status;
    _priorityFilter = priority;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _typeFilter = null;
    _statusFilter = null;
    _priorityFilter = null;
    notifyListeners();
  }

  // Métodos CRUD
  Future<void> loadReports() async {
    _setLoading(true);
    _clearError();

    try {
      // Simular datos mock para desarrollo
      await Future.delayed(const Duration(milliseconds: 500));

      _reports = _generateMockReports();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Error al cargar reportes: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<bool> createReport(ReportModel report) async {
    _setLoading(true);
    _clearError();

    try {
      // Simular creación
      await Future.delayed(const Duration(milliseconds: 800));

      final newReport = report.copyWith(
        id: 'report_${DateTime.now().millisecondsSinceEpoch}',
      );

      _reports.insert(0, newReport);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al crear reporte: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateReport(ReportModel report) async {
    _setLoading(true);
    _clearError();

    try {
      // Simular actualización
      await Future.delayed(const Duration(milliseconds: 600));

      final index = _reports.indexWhere((r) => r.id == report.id);
      if (index != -1) {
        _reports[index] = report;
        if (_selectedReport?.id == report.id) {
          _selectedReport = report;
        }
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al actualizar reporte: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> deleteReport(String reportId) async {
    _setLoading(true);
    _clearError();

    try {
      // Simular eliminación
      await Future.delayed(const Duration(milliseconds: 400));

      _reports.removeWhere((report) => report.id == reportId);
      if (_selectedReport?.id == reportId) {
        _selectedReport = null;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Error al eliminar reporte: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<ReportModel?> getReportById(String reportId) async {
    try {
      return _reports.firstWhere((report) => report.id == reportId);
    } catch (e) {
      return null;
    }
  }

  void selectReport(ReportModel? report) {
    _selectedReport = report;
    notifyListeners();
  }

  // Métodos de utilidad
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Generar datos mock
  List<ReportModel> _generateMockReports() {
    final now = DateTime.now();
    return [
      ReportModel(
        id: 'report_001',
        assetId: 'asset_001',
        assetName: 'Computadora Dell Optiplex',
        title: 'Pantalla no enciende',
        description:
            'La pantalla del monitor no enciende al presionar el botón de encendido. Se verificó que el cable de alimentación está conectado correctamente. El LED de estado del monitor permanece apagado. Se probó con diferentes cables de alimentación sin éxito. El problema comenzó esta mañana después de un corte de energía.',
        priority: 'Alta',
        status: 'En Progreso',
        type: 'Falla',
        reportedAt: now.subtract(const Duration(hours: 2)),
        reportedBy: 'Juan Pérez - Analista de Sistemas',
        assignedTo: 'Ana Técnico IT',
        assignedAt: now.subtract(const Duration(hours: 1)),
        location: 'Oficina Principal - Piso 2 - Escritorio 15',
        imageUrls: [
          '/mock/images/monitor_no_power.jpg',
          '/mock/images/power_cable_check.jpg',
        ],
        metadata: {
          'urgency_level': 'high',
          'affected_users': 1,
          'business_impact': 'medium',
          'estimated_repair_time': '2-4 hours',
        },
      ),
      ReportModel(
        id: 'report_002',
        assetId: 'asset_002',
        assetName: 'Impresora HP LaserJet',
        title: 'Atasco de papel frecuente',
        description:
            'La impresora presenta atascos de papel de manera frecuente, especialmente al imprimir documentos de varias páginas. Los atascos ocurren principalmente en la bandeja de salida. Se ha limpiado superficialmente pero el problema persiste. Afecta la productividad del área administrativa.',
        priority: 'Media',
        status: 'En Progreso',
        type: 'Falla',
        reportedAt: now.subtract(const Duration(days: 1)),
        reportedBy: 'María García - Asistente Administrativa',
        assignedTo: 'Carlos Técnico',
        assignedAt: now.subtract(const Duration(hours: 8)),
        location: 'Área de Administración - Estación de Impresión',
        imageUrls: [
          '/mock/images/printer_jam.jpg',
          '/mock/images/paper_tray_issue.jpg',
          '/mock/images/printer_error_display.jpg',
        ],
        metadata: {
          'frequency': 'daily',
          'affected_users': 8,
          'business_impact': 'medium',
          'last_maintenance': '2 months ago',
        },
      ),
      ReportModel(
        id: 'report_003',
        assetId: 'asset_003',
        assetName: 'Aire Acondicionado Central',
        title: 'Mantenimiento preventivo mensual',
        description:
            'Realizar mantenimiento preventivo mensual programado: limpieza completa de filtros HEPA, revisión de niveles de refrigerante R-410A, verificación de componentes eléctricos, calibración de termostatos y limpieza de serpentines. Incluye verificación de presiones y temperaturas del sistema.',
        priority: 'Media',
        status: 'Resuelto',
        type: 'Mantenimiento',
        reportedAt: now.subtract(const Duration(days: 3)),
        reportedBy: 'Sistema Automático de Mantenimiento',
        assignedTo: 'Pedro Mantenimiento',
        assignedAt: now.subtract(const Duration(days: 2)),
        resolvedAt: now.subtract(const Duration(hours: 4)),
        resolution:
            'Mantenimiento preventivo completado exitosamente. Filtros HEPA reemplazados, refrigerante verificado en niveles óptimos (2.1 kg), serpentines limpiados, termostatos calibrados. Sistema funcionando a 98% de eficiencia. Próximo mantenimiento programado en 30 días.',
        location: 'Edificio Principal - Azotea - Unidad Central',
        imageUrls: [
          '/mock/images/ac_filters_before.jpg',
          '/mock/images/ac_filters_after.jpg',
          '/mock/images/ac_maintenance_complete.jpg',
          '/mock/images/ac_pressure_readings.jpg',
        ],
        metadata: {
          'maintenance_type': 'preventive',
          'duration_hours': 4,
          'parts_replaced': ['Filtros HEPA', 'Sellos de goma'],
          'efficiency_rating': '98%',
          'next_maintenance': '30 days',
        },
      ),
      ReportModel(
        id: 'report_004',
        assetId: 'asset_004',
        assetName: 'Vehículo Toyota Hilux',
        title: 'Ruido metálico extraño en el motor',
        description:
            'Se escucha un ruido metálico extraño y constante proveniente del motor, especialmente al acelerar entre 2000-3000 RPM. El ruido se intensifica bajo carga y parece provenir del área del cigüeñal. El vehículo presenta ligera pérdida de potencia. Kilometraje actual: 85,420 km. URGENTE: Vehículo fuera de servicio por seguridad.',
        priority: 'Crítica',
        status: 'En Revisión',
        type: 'Falla',
        reportedAt: now.subtract(const Duration(hours: 6)),
        reportedBy: 'Luis Conductor - Operaciones',
        assignedTo: 'Taller Mecánico Especializado',
        assignedAt: now.subtract(const Duration(hours: 4)),
        location: 'Estacionamiento Principal - Bahía 3',
        imageUrls: [
          '/mock/images/engine_bay_inspection.jpg',
          '/mock/images/oil_level_check.jpg',
          '/mock/images/vehicle_dashboard_warning.jpg',
        ],
        metadata: {
          'severity': 'critical',
          'vehicle_status': 'out_of_service',
          'mileage': 85420,
          'last_service': '3 months ago',
          'estimated_repair_cost': '800-1500',
          'safety_risk': 'high',
        },
      ),
      ReportModel(
        id: 'report_005',
        assetId: 'asset_005',
        assetName: 'Servidor Principal',
        title: 'Inspección de seguridad trimestral',
        description:
            'Inspección trimestral programada de seguridad del servidor principal: verificación completa de logs de eventos, instalación de parches críticos de Windows Server 2022, revisión de configuraciones de firewall, análisis de vulnerabilidades, verificación de certificados SSL, y auditoría de accesos de usuarios. Incluye backup completo del sistema.',
        priority: 'Alta',
        status: 'En Progreso',
        type: 'Inspección',
        reportedAt: now.subtract(const Duration(hours: 12)),
        reportedBy: 'Sistema de Monitoreo Automatizado',
        assignedTo: 'Roberto Seguridad - Especialista IT',
        assignedAt: now.subtract(const Duration(hours: 8)),
        location: 'Sala de Servidores - Rack A1',
        imageUrls: [
          '/mock/images/server_rack_inspection.jpg',
          '/mock/images/security_logs_review.jpg',
        ],
        metadata: {
          'inspection_type': 'quarterly_security',
          'server_uptime': '99.8%',
          'last_backup': 'yesterday',
          'critical_patches': 3,
          'estimated_downtime': '2 hours',
        },
      ),
      ReportModel(
        id: 'report_006',
        assetId: 'asset_006',
        assetName: 'Silla de Oficina Ergonómica',
        title: 'Mecanismo de ajuste de altura defectuoso',
        description:
            'El mecanismo neumático de ajuste de altura de la silla presenta dificultad para subir y bajar. La palanca requiere fuerza excesiva y a veces no responde. El pistón neumático parece haber perdido presión. Afecta la ergonomía del puesto de trabajo. No es urgente pero requiere atención para prevenir lesiones por mala postura.',
        priority: 'Baja',
        status: 'Abierto',
        type: 'Falla',
        reportedAt: now.subtract(const Duration(days: 2)),
        reportedBy: 'Sandra Oficina - Recursos Humanos',
        assignedTo: 'Miguel Mobiliario',
        assignedAt: now.subtract(const Duration(days: 1)),
        location: 'Oficina Principal - Piso 1 - Escritorio 8',
        imageUrls: [
          '/mock/images/chair_mechanism.jpg',
          '/mock/images/chair_height_issue.jpg',
        ],
        metadata: {
          'ergonomic_impact': 'medium',
          'user_comfort': 'affected',
          'warranty_status': 'expired',
          'replacement_cost': '150-200',
        },
      ),
      ReportModel(
        id: 'report_007',
        assetId: 'asset_007',
        assetName: 'Lámpara LED Escritorio',
        title: 'Reemplazo de lámpara LED fundida',
        description:
            'Reemplazo programado de lámpara LED de escritorio que se fundió en el área de contabilidad. La lámpara de 15W dejó de funcionar completamente. Se verificó que el problema no es del cableado sino del LED interno. Trabajo completado exitosamente con lámpara de repuesto del inventario.',
        priority: 'Baja',
        status: 'Cerrado',
        type: 'Mantenimiento',
        reportedAt: now.subtract(const Duration(days: 5)),
        reportedBy: 'Ana Contabilidad - Contador Senior',
        assignedTo: 'Luis Electricista',
        assignedAt: now.subtract(const Duration(days: 4)),
        resolvedAt: now.subtract(const Duration(days: 1)),
        resolution:
            'Lámpara LED de 15W reemplazada exitosamente por modelo equivalente del inventario. Funcionamiento verificado y aprobado por el usuario. Lámpara anterior enviada a reciclaje electrónico. Tiempo de trabajo: 15 minutos.',
        location: 'Área de Contabilidad - Escritorio Principal',
        imageUrls: [
          '/mock/images/led_lamp_before.jpg',
          '/mock/images/led_lamp_after.jpg',
          '/mock/images/lamp_installation_complete.jpg',
        ],
        metadata: {
          'work_duration': '15 minutes',
          'parts_used': ['Lámpara LED 15W'],
          'cost': '25.00',
          'warranty': '2 years',
          'user_satisfaction': 'excellent',
        },
      ),
    ];
  }

  // Métodos de estadísticas
  Map<String, int> getReportsByStatus() {
    final statusCount = <String, int>{};
    for (final status in AppConstants.reportStatuses) {
      statusCount[status] = _reports.where((r) => r.status == status).length;
    }
    return statusCount;
  }

  Map<String, int> getReportsByPriority() {
    final priorityCount = <String, int>{};
    for (final priority in AppConstants.reportPriorities) {
      priorityCount[priority] = _reports
          .where((r) => r.priority == priority)
          .length;
    }
    return priorityCount;
  }

  Map<String, int> getReportsByType() {
    final typeCount = <String, int>{};
    for (final type in AppConstants.reportTypes) {
      typeCount[type] = _reports.where((r) => r.type == type).length;
    }
    return typeCount;
  }

  List<ReportModel> getRecentReports({int limit = 5}) {
    final sortedReports = List<ReportModel>.from(_reports);
    sortedReports.sort((a, b) => b.reportedAt.compareTo(a.reportedAt));
    return sortedReports.take(limit).toList();
  }

  List<ReportModel> getHighPriorityReports() {
    return _reports
        .where((r) => r.priority == 'Crítica' || r.priority == 'Alta')
        .toList();
  }

  List<ReportModel> getOpenReports() {
    return _reports
        .where((r) => r.status == 'Abierto' || r.status == 'En Progreso')
        .toList();
  }
}
