import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/backend_service.dart';
import '../../providers/auth_provider.dart';

class AppwriteTestScreen extends StatefulWidget {
  const AppwriteTestScreen({super.key});

  @override
  State<AppwriteTestScreen> createState() => _AppwriteTestScreenState();
}

class _AppwriteTestScreenState extends State<AppwriteTestScreen> {
  String _connectionStatus = 'Verificando conexión...';
  bool _isConnected = false;
  String _projectInfo = '';

  @override
  void initState() {
    super.initState();
    _testConnection();
  }

  Future<void> _testConnection() async {
    try {
      final backend = BackendService.instance;

      // Intentar obtener información del proyecto
      setState(() {
        _connectionStatus = 'Conectando al backend (${backend.serviceName})...';
      });

      // Test básico de conexión
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _isConnected = true;
        _connectionStatus = '✅ Conexión exitosa con ${backend.serviceName}';
        if (backend.isUsingAppwrite) {
          _projectInfo =
              'Endpoint: ${backend.client.config['endpoint']}\n'
              'Project: ${backend.client.config['project']}';
        } else {
          _projectInfo =
              'Modo: Desarrollo con Mock Service\n'
              'Endpoint: Mock Backend para Android';
        }
      });
    } catch (e) {
      setState(() {
        _isConnected = false;
        _connectionStatus = '❌ Error de conexión: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Appwrite'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Estado de conexión
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Estado de Conexión',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _connectionStatus,
                      style: TextStyle(
                        color: _isConnected ? Colors.green : Colors.red,
                        fontSize: 16,
                      ),
                    ),
                    if (_projectInfo.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        _projectInfo,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test de autenticación
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Test de Autenticación',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Consumer<AuthProvider>(
                      builder: (context, authProvider, _) {
                        return Column(
                          children: [
                            Text(
                              authProvider.isAuthenticated
                                  ? '✅ Usuario autenticado'
                                  : '❌ Usuario no autenticado',
                              style: TextStyle(
                                color: authProvider.isAuthenticated
                                    ? Colors.green
                                    : Colors.orange,
                                fontSize: 16,
                              ),
                            ),
                            if (authProvider.currentUser != null) ...[
                              const SizedBox(height: 8),
                              Text(
                                'Email: ${authProvider.currentUser!.email}',
                                style: const TextStyle(fontSize: 14),
                              ),
                              Text(
                                'Rol: ${authProvider.currentUser!.role}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ],
                            if (authProvider.errorMessage != null) ...[
                              const SizedBox(height: 8),
                              Text(
                                'Error: ${authProvider.errorMessage}',
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Botones de acción
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Acciones de Prueba',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _testConnection,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Probar Conexión'),
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          // Navegar a pantalla de login cuando esté lista
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Pantalla de login próximamente'),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.secondary,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Ir a Login'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const Spacer(),

            // Información de configuración
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '📋 Configuración Requerida',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '1. Configura tu Project ID en app_constants.dart\n'
                      '2. Crea la base de datos en Appwrite\n'
                      '3. Configura las colecciones según setup_appwrite.md\n'
                      '4. Habilita autenticación Email/Password',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
