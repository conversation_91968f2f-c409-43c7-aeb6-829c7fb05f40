# 📱 Guía de Uso - MTTO 60

## 🎉 ¡Aplicación Lista!

Tu aplicación MTTO 60 está funcionando perfectamente con las pantallas principales implementadas.

## 🚀 Cómo Usar la Aplicación

### 1. **Pantalla de Login**
Al abrir la aplicación verás la pantalla de login con:
- ✅ Logo y branding de MTTO 60
- ✅ Formulario de email y contraseña
- ✅ Validaciones en tiempo real
- ✅ Enlace para registrarse
- ✅ Información de modo desarrollo

**Para probar (Modo Desarrollo):**
- Email: `<EMAIL>`
- Contraseña: `password`
- O cualquier email válido con contraseña de 6+ caracteres

### 2. **Pantalla de Registro**
- ✅ Formulario completo con validaciones
- ✅ Selector de rol (Admin, Técnico, Reportador)
- ✅ Confirmación de contraseña
- ✅ Navegación de vuelta al login

### 3. **Dashboard Principal**
Una vez autenticado verás:
- ✅ Saludo personalizado con avatar
- ✅ Tarjetas de resumen (Activos, Reportes, Órdenes)
- ✅ Acciones rápidas
- ✅ Navegación por pestañas
- ✅ Menú de perfil y logout

### 4. **Navegación por Pestañas**
- 🏠 **Dashboard**: Resumen y acciones rápidas
- 📦 **Activos**: Lista de activos (próximamente)
- 📋 **Reportes**: Lista de reportes (próximamente)
- 🔧 **Órdenes**: Órdenes de trabajo (próximamente)

## 🔧 Funcionalidades Implementadas

### ✅ **Autenticación Completa**
- Login con validaciones
- Registro de usuarios
- Gestión de sesiones
- Logout seguro
- Manejo de errores

### ✅ **Dashboard Funcional**
- Información del usuario
- Resumen de datos
- Navegación intuitiva
- Acciones rápidas
- Diseño responsive

### ✅ **UI/UX Profesional**
- Material Design 3
- Colores corporativos
- Iconografía consistente
- Animaciones suaves
- Feedback visual

### ✅ **Arquitectura Sólida**
- Clean Architecture
- Provider para estado
- Widgets reutilizables
- Separación de responsabilidades

## 📱 Comandos para Desarrollo

```bash
# Ejecutar en Android
flutter run

# Ejecutar en web
flutter run -d chrome

# Hot reload (durante desarrollo)
r

# Hot restart
R

# Análisis de código
flutter analyze

# Limpiar proyecto
flutter clean && flutter pub get
```

## 🎯 Próximas Funcionalidades a Implementar

### 1. **Gestión de Activos** (Próxima)
- Lista de activos con búsqueda
- Formulario de creación/edición
- Detalles de activo
- Historial de mantenimiento

### 2. **Sistema de Reportes**
- Formulario de reporte de fallas
- Lista de reportes con filtros
- Asignación de prioridades
- Adjuntar imágenes

### 3. **Órdenes de Trabajo**
- Creación desde reportes
- Asignación a técnicos
- Seguimiento de progreso
- Firma digital

### 4. **Funcionalidades Avanzadas**
- Escáner QR (cuando se reactive)
- Notificaciones push
- Generación de PDFs
- Modo offline

## 🔄 Datos de Prueba (Mock)

En modo desarrollo, la aplicación usa datos simulados:
- **Usuarios**: Cualquier email/contraseña funciona
- **Activos**: 24 activos simulados
- **Reportes**: 8 reportes de prueba
- **Órdenes**: 12 órdenes simuladas

## 🛠️ Personalización

### Cambiar Colores
Edita `lib/core/constants/app_colors.dart`:
```dart
static const Color primary = Color(0xFF1976D2); // Tu color principal
```

### Cambiar Textos
Edita `lib/core/constants/app_strings.dart`:
```dart
static const String appName = 'Tu App Name';
```

### Agregar Nuevas Pantallas
1. Crear archivo en `lib/presentation/screens/`
2. Agregar navegación en el dashboard
3. Implementar funcionalidad

## 🎉 Estado Actual

**✅ Completado:**
- Autenticación completa
- Dashboard funcional
- Navegación por pestañas
- UI/UX profesional
- Arquitectura sólida

**🔄 En Desarrollo:**
- Gestión de activos
- Sistema de reportes
- Órdenes de trabajo

**📋 Pendiente:**
- Integración con Appwrite real
- Funcionalidades avanzadas
- Testing completo

## 📞 Desarrollo Continuo

Para continuar el desarrollo:
1. **Implementar gestión de activos** (siguiente prioridad)
2. **Agregar sistema de reportes**
3. **Desarrollar órdenes de trabajo**
4. **Integrar Appwrite real** cuando esté listo
5. **Agregar funcionalidades avanzadas**

¡Tu aplicación MTTO 60 está lista para el siguiente nivel de desarrollo! 🚀
