# MTTO 60 - Sistema de Mantenimiento de Activos

Una aplicación Flutter completa para la gestión integral de mantenimiento de activos, incluyendo vehículos, herramientas, equipos e infraestructura.

## 🚀 Características Principales

### 📋 Gestión de Activos
- Registro completo de vehículos, herramientas, equipos e infraestructura
- Clasificación por categorías, tipos y etiquetas personalizables
- Historial detallado de cada activo
- Sistema de identificación por QR/Barcode
- Gestión de documentos y certificaciones

### 🔧 Reporte de Fallas
- Formulario intuitivo para reportar problemas
- Adjuntar fotos y evidencias
- Sistema de priorización automática
- Asignación inteligente según gravedad y disponibilidad

### 📝 Órdenes de Trabajo
- Creación automática desde reportes
- Asignación a técnicos especializados
- Checklist de tareas personalizables
- Registro de tiempo y recursos utilizados
- Firma digital de terminación

### ⚙️ Mantenimiento Preventivo
- Programación de mantenimientos recurrentes
- Alertas y notificaciones automáticas
- Checklist estandarizados por tipo de activo
- Histórico completo de mantenimientos

### 📊 Documentación e Informes
- Generación automática de PDFs
- Plantillas personalizables
- Almacenamiento seguro en la nube
- Compartir documentos por email/chat
- Dashboard con métricas y KPIs

## 🏗️ Arquitectura

El proyecto sigue los principios de **Clean Architecture** con la siguiente estructura:

```
lib/
├── core/                    # Funcionalidades compartidas
│   ├── constants/          # Constantes de la aplicación
│   ├── errors/             # Manejo de errores
│   ├── network/            # Configuración de red
│   ├── utils/              # Utilidades generales
│   └── widgets/            # Widgets reutilizables
├── data/                   # Capa de datos
│   ├── datasources/        # Fuentes de datos (API, local)
│   ├── models/             # Modelos de datos
│   └── repositories/       # Implementación de repositorios
├── domain/                 # Lógica de negocio
│   ├── entities/           # Entidades del dominio
│   ├── repositories/       # Interfaces de repositorios
│   └── usecases/           # Casos de uso
└── presentation/           # Capa de presentación
    ├── providers/          # Gestión de estado (Provider)
    ├── screens/            # Pantallas de la aplicación
    └── widgets/            # Widgets específicos de UI
```

## 🛠️ Tecnologías Utilizadas

### Frontend
- **Flutter 3.8+** - Framework de desarrollo multiplataforma
- **Provider** - Gestión de estado reactiva
- **Material Design 3** - Sistema de diseño moderno

### Backend
- **Appwrite** - Backend as a Service
- **Appwrite Database** - Base de datos NoSQL
- **Appwrite Auth** - Sistema de autenticación
- **Appwrite Storage** - Almacenamiento de archivos
- **Appwrite Functions** - Funciones serverless

### Funcionalidades Adicionales
- **PDF Generation** - Generación de documentos
- **QR/Barcode Scanner** - Lectura de códigos
- **Image Picker** - Captura y selección de imágenes
- **Local Notifications** - Notificaciones locales
- **Offline Support** - Funcionalidad sin conexión

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
