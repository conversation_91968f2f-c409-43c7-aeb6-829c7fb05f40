import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/utils/app_utils.dart';
import '../../../data/models/asset_model.dart';
import '../../providers/asset_provider.dart';

class AssetFormScreen extends StatefulWidget {
  final AssetModel? asset;

  const AssetFormScreen({super.key, this.asset});

  @override
  State<AssetFormScreen> createState() => _AssetFormScreenState();
}

class _AssetFormScreenState extends State<AssetFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _serialNumberController = TextEditingController();
  final _modelController = TextEditingController();
  final _brandController = TextEditingController();
  final _purchaseDateController = TextEditingController();
  final _purchasePriceController = TextEditingController();

  String _selectedType = AppConstants.assetTypes.first;
  String _selectedStatus = AppConstants.assetStatuses.first;
  DateTime? _selectedPurchaseDate;

  // Campos específicos para vehículos
  final TextEditingController _chassisController = TextEditingController();
  final TextEditingController _motorController = TextEditingController();
  final TextEditingController _lineaController = TextEditingController();
  final TextEditingController _cilindrajeController = TextEditingController();
  final TextEditingController _capacidadController = TextEditingController();
  final TextEditingController _colorController = TextEditingController();
  final TextEditingController _licenciaTransitoController =
      TextEditingController();
  final TextEditingController _importacionController = TextEditingController();

  String _selectedClaseVehiculo = 'Automóvil';
  String _selectedServicio = 'Particular';
  String _selectedTipoCarroceria = 'Especial';
  String _selectedCombustible = 'Gasolina';

  DateTime? _soatVencimiento;
  DateTime? _polizaVencimiento;
  DateTime? _tecnomecanicaVencimiento;
  bool _aplicaPoliza = false;

  // Listas para campos de vehículos
  final List<String> _clasesVehiculo = [
    'Automóvil',
    'Camión',
    'Moto',
    'Camioneta',
  ];

  final List<String> _servicios = [
    'Particular',
    'Público',
    'Oficial',
    'Diplomático',
  ];

  final List<String> _tiposCarroceria = [
    'Especial',
    'Sedan',
    'Hatchback',
    'SUV',
    'Pickup',
    'Van',
  ];

  final List<String> _combustibles = [
    'Gasolina',
    'Diesel',
    'Eléctrico',
    'Híbrido',
    'GLP',
  ];

  bool get _isEditing => widget.asset != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadAssetData();
    }
  }

  void _loadAssetData() {
    final asset = widget.asset!;
    _nameController.text = asset.name;
    _descriptionController.text = asset.description ?? '';
    _locationController.text = asset.location ?? '';
    _serialNumberController.text = asset.serialNumber ?? '';
    _modelController.text = asset.model ?? '';
    _brandController.text = asset.brand ?? '';
    _purchasePriceController.text = asset.purchasePrice?.toString() ?? '';
    _selectedType = asset.type;
    _selectedStatus = asset.status;
    _selectedPurchaseDate = asset.purchaseDate;
    if (_selectedPurchaseDate != null) {
      _purchaseDateController.text = AppUtils.formatDate(
        _selectedPurchaseDate!,
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _serialNumberController.dispose();
    _modelController.dispose();
    _brandController.dispose();
    _purchaseDateController.dispose();
    _purchasePriceController.dispose();
    super.dispose();
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    final assetProvider = Provider.of<AssetProvider>(context, listen: false);

    final asset = AssetModel(
      id: _isEditing ? widget.asset!.id : '',
      code: _isEditing ? widget.asset!.code : '',
      name: _nameController.text.trim(),
      type: _selectedType,
      status: _selectedStatus,
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      location: _locationController.text.trim().isEmpty
          ? null
          : _locationController.text.trim(),
      serialNumber: _serialNumberController.text.trim().isEmpty
          ? null
          : _serialNumberController.text.trim(),
      model: _modelController.text.trim().isEmpty
          ? null
          : _modelController.text.trim(),
      brand: _brandController.text.trim().isEmpty
          ? null
          : _brandController.text.trim(),
      purchaseDate: _selectedPurchaseDate,
      purchasePrice: _purchasePriceController.text.isNotEmpty
          ? double.tryParse(_purchasePriceController.text)
          : null,
      createdAt: _isEditing ? widget.asset!.createdAt : DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: _isEditing
          ? widget.asset!.createdBy
          : 'current_user', // TODO: Obtener usuario actual
    );

    bool success;
    if (_isEditing) {
      success = await assetProvider.updateAsset(asset);
    } else {
      success = await assetProvider.createAsset(asset);
    }

    if (mounted) {
      if (success) {
        AppUtils.showSuccessSnackBar(
          context,
          _isEditing
              ? 'Activo actualizado exitosamente'
              : 'Activo creado exitosamente',
        );
        Navigator.of(context).pop();
      } else {
        AppUtils.showErrorSnackBar(
          context,
          assetProvider.errorMessage ?? 'Error al guardar el activo',
        );
      }
    }
  }

  Future<void> _selectPurchaseDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedPurchaseDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _selectedPurchaseDate = date;
        _purchaseDateController.text = AppUtils.formatDate(date);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(_isEditing ? 'Editar Activo' : 'Nuevo Activo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Información básica
              _buildSectionTitle('Información Básica'),
              const SizedBox(height: 16),

              CustomTextField(
                label: 'Nombre del Activo *',
                hint: 'Ej: Computadora Dell Optiplex',
                controller: _nameController,
                prefixIcon: const Icon(Icons.inventory_2),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'El nombre es requerido';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Tipo de activo
              _buildAssetTypeSection(),

              // Campos específicos para vehículos
              if (_selectedType == 'Vehículo') ...[
                const SizedBox(height: 24),
                _buildVehicleSpecificSection(),
              ],

              const SizedBox(height: 16),

              // Estado
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Estado *',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _selectedStatus,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.info),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    items: AppConstants.assetStatuses.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(status),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedStatus = value!;
                      });
                    },
                  ),
                ],
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Ubicación',
                hint: 'Ej: Oficina Principal - Piso 2',
                controller: _locationController,
                prefixIcon: const Icon(Icons.location_on),
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Descripción',
                hint: 'Descripción detallada del activo',
                controller: _descriptionController,
                prefixIcon: const Icon(Icons.description),
                maxLines: 3,
              ),

              const SizedBox(height: 24),

              // Información técnica
              _buildSectionTitle('Información Técnica'),
              const SizedBox(height: 16),

              CustomTextField(
                label: 'Número de Serie',
                hint: 'Ej: ABC123456789',
                controller: _serialNumberController,
                prefixIcon: const Icon(Icons.qr_code),
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Modelo',
                hint: 'Ej: Optiplex 7090',
                controller: _modelController,
                prefixIcon: const Icon(Icons.model_training),
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Marca',
                hint: 'Ej: Dell',
                controller: _brandController,
                prefixIcon: const Icon(Icons.business),
              ),

              const SizedBox(height: 24),

              // Información financiera
              _buildSectionTitle('Información Financiera'),
              const SizedBox(height: 16),

              CustomTextField(
                label: 'Fecha de Compra',
                hint: 'Seleccionar fecha',
                controller: _purchaseDateController,
                prefixIcon: const Icon(Icons.calendar_today),
                readOnly: true,
                onTap: _selectPurchaseDate,
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Precio de Compra',
                hint: 'Ej: 1500.00',
                controller: _purchasePriceController,
                prefixIcon: const Icon(Icons.attach_money),
                keyboardType: TextInputType.number,
              ),

              const SizedBox(height: 32),

              // Botón de guardar
              Consumer<AssetProvider>(
                builder: (context, assetProvider, _) {
                  return CustomButton(
                    text: _isEditing ? 'Actualizar Activo' : 'Crear Activo',
                    onPressed: assetProvider.isLoading ? null : _handleSave,
                    isLoading: assetProvider.isLoading,
                    width: double.infinity,
                  );
                },
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildAssetTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tipo de Activo *',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedType,
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.category),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          items: AppConstants.assetTypes.map((type) {
            return DropdownMenuItem(value: type, child: Text(type));
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedType = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildVehicleSpecificSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Información Específica del Vehículo',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 16),

          // Primera fila: Chasis y Motor
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  label: 'Número de Chasis',
                  hint: 'Ej: 1HGBH41JXMN109186',
                  controller: _chassisController,
                  prefixIcon: const Icon(Icons.confirmation_number),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  label: 'Número de Motor',
                  hint: 'Ej: 4G93T12345',
                  controller: _motorController,
                  prefixIcon: const Icon(Icons.settings),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Segunda fila: Línea y Cilindraje
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  label: 'Línea',
                  hint: 'Ej: Civic, Corolla',
                  controller: _lineaController,
                  prefixIcon: const Icon(Icons.directions_car),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  label: 'Cilindraje',
                  hint: 'Ej: 1600',
                  controller: _cilindrajeController,
                  prefixIcon: const Icon(Icons.speed),
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Tercera fila: Clase de Vehículo y Servicio
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Clase de Vehículo',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedClaseVehiculo,
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.directions_car),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: _clasesVehiculo.map((clase) {
                        return DropdownMenuItem(
                          value: clase,
                          child: Text(clase),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedClaseVehiculo = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Tipo de Servicio',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedServicio,
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.work),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: _servicios.map((servicio) {
                        return DropdownMenuItem(
                          value: servicio,
                          child: Text(servicio),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedServicio = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Cuarta fila: Tipo de Carrocería y Capacidad
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Tipo de Carrocería',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedTipoCarroceria,
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.airport_shuttle),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: _tiposCarroceria.map((tipo) {
                        return DropdownMenuItem(value: tipo, child: Text(tipo));
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedTipoCarroceria = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  label: 'Capacidad',
                  hint: 'Ej: 5 pasajeros',
                  controller: _capacidadController,
                  prefixIcon: const Icon(Icons.people),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quinta fila: Combustible y Color
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Tipo de Combustible',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedCombustible,
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.local_gas_station),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: _combustibles.map((combustible) {
                        return DropdownMenuItem(
                          value: combustible,
                          child: Text(combustible),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCombustible = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  label: 'Color',
                  hint: 'Ej: Blanco',
                  controller: _colorController,
                  prefixIcon: const Icon(Icons.palette),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Sexta fila: Licencia de Tránsito e Importación
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  label: 'Licencia de Tránsito',
                  hint: 'Número de licencia',
                  controller: _licenciaTransitoController,
                  prefixIcon: const Icon(Icons.card_membership),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  label: 'Importación',
                  hint: 'Número de importación',
                  controller: _importacionController,
                  prefixIcon: const Icon(Icons.flight_land),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Sección de documentos
          const Text(
            'Documentos del Vehículo',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 16),

          // SOAT
          CustomTextField(
            label: 'SOAT - Fecha de Vencimiento *',
            hint: 'Seleccionar fecha',
            controller: TextEditingController(
              text: _soatVencimiento != null
                  ? AppUtils.formatDate(_soatVencimiento!)
                  : '',
            ),
            prefixIcon: const Icon(Icons.security),
            readOnly: true,
            onTap: () => _selectDate('soat'),
          ),
          const SizedBox(height: 16),

          // Póliza (opcional)
          Row(
            children: [
              Checkbox(
                value: _aplicaPoliza,
                onChanged: (value) {
                  setState(() {
                    _aplicaPoliza = value!;
                    if (!_aplicaPoliza) {
                      _polizaVencimiento = null;
                    }
                  });
                },
              ),
              const Text('Aplica Póliza Integral'),
            ],
          ),
          if (_aplicaPoliza) ...[
            const SizedBox(height: 8),
            CustomTextField(
              label: 'Póliza - Fecha de Vencimiento',
              hint: 'Seleccionar fecha',
              controller: TextEditingController(
                text: _polizaVencimiento != null
                    ? AppUtils.formatDate(_polizaVencimiento!)
                    : '',
              ),
              prefixIcon: const Icon(Icons.shield),
              readOnly: true,
              onTap: () => _selectDate('poliza'),
            ),
          ],
          const SizedBox(height: 16),

          // Tecnomecánica
          CustomTextField(
            label: 'Tecnomecánica - Fecha de Vencimiento *',
            hint: 'Seleccionar fecha',
            controller: TextEditingController(
              text: _tecnomecanicaVencimiento != null
                  ? AppUtils.formatDate(_tecnomecanicaVencimiento!)
                  : '',
            ),
            prefixIcon: const Icon(Icons.build_circle),
            readOnly: true,
            onTap: () => _selectDate('tecnomecanica'),
          ),
        ],
      ),
    );
  }

  void _selectDate(String type) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );

    if (picked != null) {
      setState(() {
        switch (type) {
          case 'soat':
            _soatVencimiento = picked;
            break;
          case 'poliza':
            _polizaVencimiento = picked;
            break;
          case 'tecnomecanica':
            _tecnomecanicaVencimiento = picked;
            break;
        }
      });
    }
  }
}
