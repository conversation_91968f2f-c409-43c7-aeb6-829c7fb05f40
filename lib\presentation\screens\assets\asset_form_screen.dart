import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/utils/app_utils.dart';
import '../../../data/models/asset_model.dart';
import '../../providers/asset_provider.dart';

class AssetFormScreen extends StatefulWidget {
  final AssetModel? asset;

  const AssetFormScreen({super.key, this.asset});

  @override
  State<AssetFormScreen> createState() => _AssetFormScreenState();
}

class _AssetFormScreenState extends State<AssetFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _serialNumberController = TextEditingController();
  final _modelController = TextEditingController();
  final _brandController = TextEditingController();
  final _purchaseDateController = TextEditingController();
  final _purchasePriceController = TextEditingController();

  String _selectedType = AppConstants.assetTypes.first;
  String _selectedStatus = AppConstants.assetStatuses.first;
  DateTime? _selectedPurchaseDate;

  // Variables para formulario jerárquico
  String? _selectedCategory;
  String? _selectedSubcategory;
  String? _selectedUnit;

  // Mapas de datos jerárquicos
  final Map<String, List<String>> _subcategories = {
    'Vehículos': ['Unidades', 'Camionetas'],
    'Equipo': ['Herramientas', 'Maquinaria', 'Electrónicos'],
    'Infraestructura': ['Edificios', 'Instalaciones', 'Servicios'],
  };

  final Map<String, List<String>> _units = {
    'Unidades': ['Horus', 'JI', 'Thot', 'Alfa', 'Seth', 'Rho'],
  };

  bool get _isEditing => widget.asset != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadAssetData();
    }
  }

  void _loadAssetData() {
    final asset = widget.asset!;
    _nameController.text = asset.name;
    _descriptionController.text = asset.description ?? '';
    _locationController.text = asset.location ?? '';
    _serialNumberController.text = asset.serialNumber ?? '';
    _modelController.text = asset.model ?? '';
    _brandController.text = asset.brand ?? '';
    _purchasePriceController.text = asset.purchasePrice?.toString() ?? '';
    _selectedType = asset.type;
    _selectedStatus = asset.status;
    _selectedPurchaseDate = asset.purchaseDate;
    if (_selectedPurchaseDate != null) {
      _purchaseDateController.text = AppUtils.formatDate(
        _selectedPurchaseDate!,
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _serialNumberController.dispose();
    _modelController.dispose();
    _brandController.dispose();
    _purchaseDateController.dispose();
    _purchasePriceController.dispose();
    super.dispose();
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    final assetProvider = Provider.of<AssetProvider>(context, listen: false);

    final asset = AssetModel(
      id: _isEditing ? widget.asset!.id : '',
      code: _isEditing ? widget.asset!.code : '',
      name: _nameController.text.trim(),
      type: _selectedType,
      status: _selectedStatus,
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      location: _locationController.text.trim().isEmpty
          ? null
          : _locationController.text.trim(),
      serialNumber: _serialNumberController.text.trim().isEmpty
          ? null
          : _serialNumberController.text.trim(),
      model: _modelController.text.trim().isEmpty
          ? null
          : _modelController.text.trim(),
      brand: _brandController.text.trim().isEmpty
          ? null
          : _brandController.text.trim(),
      purchaseDate: _selectedPurchaseDate,
      purchasePrice: _purchasePriceController.text.isNotEmpty
          ? double.tryParse(_purchasePriceController.text)
          : null,
      createdAt: _isEditing ? widget.asset!.createdAt : DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: _isEditing
          ? widget.asset!.createdBy
          : 'current_user', // TODO: Obtener usuario actual
    );

    bool success;
    if (_isEditing) {
      success = await assetProvider.updateAsset(asset);
    } else {
      success = await assetProvider.createAsset(asset);
    }

    if (mounted) {
      if (success) {
        AppUtils.showSuccessSnackBar(
          context,
          _isEditing
              ? 'Activo actualizado exitosamente'
              : 'Activo creado exitosamente',
        );
        Navigator.of(context).pop();
      } else {
        AppUtils.showErrorSnackBar(
          context,
          assetProvider.errorMessage ?? 'Error al guardar el activo',
        );
      }
    }
  }

  Future<void> _selectPurchaseDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedPurchaseDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _selectedPurchaseDate = date;
        _purchaseDateController.text = AppUtils.formatDate(date);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(_isEditing ? 'Editar Activo' : 'Nuevo Activo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Información básica
              _buildSectionTitle('Información Básica'),
              const SizedBox(height: 16),

              CustomTextField(
                label: 'Nombre del Activo *',
                hint: 'Ej: Computadora Dell Optiplex',
                controller: _nameController,
                prefixIcon: const Icon(Icons.inventory_2),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'El nombre es requerido';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Clasificación jerárquica
              _buildHierarchicalClassification(),

              const SizedBox(height: 16),

              // Estado
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Estado *',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _selectedStatus,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.info),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    items: AppConstants.assetStatuses.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(status),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedStatus = value!;
                      });
                    },
                  ),
                ],
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Ubicación',
                hint: 'Ej: Oficina Principal - Piso 2',
                controller: _locationController,
                prefixIcon: const Icon(Icons.location_on),
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Descripción',
                hint: 'Descripción detallada del activo',
                controller: _descriptionController,
                prefixIcon: const Icon(Icons.description),
                maxLines: 3,
              ),

              const SizedBox(height: 24),

              // Información técnica
              _buildSectionTitle('Información Técnica'),
              const SizedBox(height: 16),

              CustomTextField(
                label: 'Número de Serie',
                hint: 'Ej: ABC123456789',
                controller: _serialNumberController,
                prefixIcon: const Icon(Icons.qr_code),
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Modelo',
                hint: 'Ej: Optiplex 7090',
                controller: _modelController,
                prefixIcon: const Icon(Icons.model_training),
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Marca',
                hint: 'Ej: Dell',
                controller: _brandController,
                prefixIcon: const Icon(Icons.business),
              ),

              const SizedBox(height: 24),

              // Información financiera
              _buildSectionTitle('Información Financiera'),
              const SizedBox(height: 16),

              CustomTextField(
                label: 'Fecha de Compra',
                hint: 'Seleccionar fecha',
                controller: _purchaseDateController,
                prefixIcon: const Icon(Icons.calendar_today),
                readOnly: true,
                onTap: _selectPurchaseDate,
              ),

              const SizedBox(height: 16),

              CustomTextField(
                label: 'Precio de Compra',
                hint: 'Ej: 1500.00',
                controller: _purchasePriceController,
                prefixIcon: const Icon(Icons.attach_money),
                keyboardType: TextInputType.number,
              ),

              const SizedBox(height: 32),

              // Botón de guardar
              Consumer<AssetProvider>(
                builder: (context, assetProvider, _) {
                  return CustomButton(
                    text: _isEditing ? 'Actualizar Activo' : 'Crear Activo',
                    onPressed: assetProvider.isLoading ? null : _handleSave,
                    isLoading: assetProvider.isLoading,
                    width: double.infinity,
                  );
                },
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildHierarchicalClassification() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Categoría principal
        const Text(
          'Categoría *',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.category),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            hintText: 'Seleccionar categoría',
          ),
          items: _subcategories.keys.map((category) {
            return DropdownMenuItem(value: category, child: Text(category));
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategory = value;
              _selectedSubcategory = null; // Reset subcategoría
              _selectedUnit = null; // Reset unidad
              _selectedType = value ?? AppConstants.assetTypes.first;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'La categoría es requerida';
            }
            return null;
          },
        ),

        // Subcategoría (si aplica)
        if (_selectedCategory != null &&
            _subcategories[_selectedCategory]!.isNotEmpty) ...[
          const SizedBox(height: 16),
          const Text(
            'Subcategoría *',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedSubcategory,
            decoration: InputDecoration(
              prefixIcon: const Icon(Icons.subdirectory_arrow_right),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: 'Seleccionar subcategoría',
            ),
            items: _subcategories[_selectedCategory]!.map((subcategory) {
              return DropdownMenuItem(
                value: subcategory,
                child: Text(subcategory),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedSubcategory = value;
                _selectedUnit = null; // Reset unidad
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'La subcategoría es requerida';
              }
              return null;
            },
          ),
        ],

        // Unidad específica (solo para Unidades)
        if (_selectedSubcategory == 'Unidades') ...[
          const SizedBox(height: 16),
          const Text(
            'Unidad Específica *',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedUnit,
            decoration: InputDecoration(
              prefixIcon: const Icon(Icons.precision_manufacturing),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: 'Seleccionar unidad',
            ),
            items: _units['Unidades']!.map((unit) {
              return DropdownMenuItem(value: unit, child: Text(unit));
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedUnit = value;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'La unidad es requerida';
              }
              return null;
            },
          ),
        ],

        // Campos adicionales según el tipo
        if (_selectedCategory == 'Vehículos' &&
            _selectedSubcategory == 'Camionetas') ...[
          const SizedBox(height: 16),
          CustomTextField(
            label: 'Proveedor',
            hint: 'Nombre del proveedor',
            controller: _brandController, // Reutilizar campo marca
            prefixIcon: const Icon(Icons.business),
          ),
          const SizedBox(height: 16),
          CustomTextField(
            label: 'Placa',
            hint: 'Número de placa',
            controller: _serialNumberController, // Reutilizar campo serie
            prefixIcon: const Icon(Icons.confirmation_number),
          ),
        ],

        // Información adicional para equipos
        if (_selectedCategory == 'Equipo') ...[
          const SizedBox(height: 16),
          CustomTextField(
            label: 'Código de Equipo',
            hint: 'Código único del equipo',
            controller: _serialNumberController,
            prefixIcon: const Icon(Icons.qr_code),
          ),
        ],

        // Información para infraestructura
        if (_selectedCategory == 'Infraestructura') ...[
          const SizedBox(height: 16),
          CustomTextField(
            label: 'Nombre de la Instalación',
            hint: 'Nombre específico',
            controller: _modelController, // Reutilizar campo modelo
            prefixIcon: const Icon(Icons.domain),
          ),
        ],
      ],
    );
  }
}
