import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../../data/models/report_model.dart';

class EnhancedPdfService {
  static Future<File> generateReportPdf(ReportModel report) async {
    final pdf = pw.Document();
    final now = DateTime.now();
    final dateFormatter = DateFormat('dd/MM/yyyy');

    // Generar número de reporte único
    final reportNumber =
        'N2SET-${now.millisecondsSinceEpoch.toString().substring(8)}';

    // Dividir descripción en múltiples fallas si es necesario
    final descriptions = _splitDescriptions(report.description);
    final hasImages = report.imageUrls.isNotEmpty;

    // Página 1: Formulario principal
    pdf.addPage(
      _buildMainPage(
        report,
        reportNumber,
        dateFormatter.format(now),
        descriptions,
      ),
    );

    // Página 2: Registro fotográfico (si hay imágenes)
    if (hasImages) {
      pdf.addPage(
        _buildPhotographicPage(report, reportNumber, dateFormatter.format(now)),
      );
    }

    // Página 3: Registro fotográfico adicional (si hay más de 5 descripciones con imágenes)
    if (descriptions.length > 5 && hasImages) {
      pdf.addPage(
        _buildAdditionalPhotographicPage(
          report,
          reportNumber,
          dateFormatter.format(now),
        ),
      );
    }

    // Guardar el PDF
    final output = await getApplicationDocumentsDirectory();
    final file = File('${output.path}/reporte_$reportNumber.pdf');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  static List<String> _splitDescriptions(String description) {
    // Si la descripción es muy larga, dividirla en partes
    if (description.length > 200) {
      final words = description.split(' ');
      final descriptions = <String>[];
      String currentDesc = '';

      for (final word in words) {
        if ((currentDesc + word).length > 200) {
          descriptions.add(currentDesc.trim());
          currentDesc = word + ' ';
        } else {
          currentDesc += word + ' ';
        }
      }

      if (currentDesc.isNotEmpty) {
        descriptions.add(currentDesc.trim());
      }

      return descriptions.take(10).toList(); // Máximo 10 descripciones
    }

    return [description];
  }

  static pw.Page _buildMainPage(
    ReportModel report,
    String reportNumber,
    String date,
    List<String> descriptions,
  ) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(20),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Encabezado
            _buildHeader(report.type, reportNumber, date),
            pw.SizedBox(height: 15),

            // Información del reporte
            _buildReportInfo(report, date),
            pw.SizedBox(height: 15),

            // Clasificación del activo
            _buildAssetClassification(report),
            pw.SizedBox(height: 15),

            // Nivel de urgencia
            _buildUrgencyLevel(report.priority),
            pw.SizedBox(height: 15),

            // Descripción de fallas
            _buildFaultsDescription(descriptions),
            pw.SizedBox(height: 15),

            // Sistemas afectados
            _buildAffectedSystems(),
            pw.SizedBox(height: 15),

            // Observaciones
            _buildObservations(),
            pw.SizedBox(height: 15),

            // Firmas
            _buildSignatures(),
            pw.SizedBox(height: 10),

            // Pie de página
            _buildFooter(reportNumber, date),
          ],
        );
      },
    );
  }

  static pw.Widget _buildHeader(
    String reportType,
    String reportNumber,
    String date,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black, width: 2),
      ),
      child: pw.Row(
        children: [
          // Logo
          pw.Container(
            width: 80,
            height: 60,
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.black),
              color: PdfColors.red,
            ),
            child: pw.Center(
              child: pw.Text(
                'NEXT\nVIEW',
                style: pw.TextStyle(
                  fontSize: 10,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ),
          pw.SizedBox(width: 20),

          // Título principal
          pw.Expanded(
            child: pw.Center(
              child: pw.Text(
                'SOLICITUD DE TRABAJO O REPORTE DE FALLA',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ),

          // Información de versión
          pw.Container(
            width: 120,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text('Versión: 002', style: const pw.TextStyle(fontSize: 8)),
                pw.Text('Fecha: $date', style: const pw.TextStyle(fontSize: 8)),
                pw.Text(
                  'Código: CO-F-01-09',
                  style: const pw.TextStyle(fontSize: 8),
                ),
                pw.Text(
                  'Celular: 3118406413',
                  style: const pw.TextStyle(fontSize: 8),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildReportInfo(ReportModel report, String date) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        children: [
          // Checkboxes de tipo
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
            ),
            child: pw.Row(
              children: [
                _buildCheckbox('REPORTE DE FALLA', report.type == 'Falla'),
                pw.SizedBox(width: 30),
                _buildCheckbox(
                  'SOLICITUD DE TRABAJO',
                  report.type == 'Mantenimiento',
                ),
              ],
            ),
          ),

          // Primera fila de información
          pw.Row(
            children: [
              _buildInfoCell('QUIEN REPORTA:', report.reportedBy, flex: 3),
              _buildInfoCell('CARGO:', 'Técnico', flex: 2),
              _buildInfoCell('CELULAR:', '3118406413', flex: 2),
            ],
          ),

          // Segunda fila de información
          pw.Row(
            children: [
              _buildInfoCell(
                'UBICACIÓN:',
                report.location ?? 'No especificada',
                flex: 3,
              ),
              _buildInfoCell('PLACA/\nCÓDIGO:', report.assetId, flex: 2),
              _buildInfoCell('FECHA:', date, flex: 2),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildAssetClassification(ReportModel report) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        children: [
          // Checkboxes de categoría
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Row(
              children: [
                _buildCheckbox('UNIDAD', false),
                pw.SizedBox(width: 15),
                _buildCheckbox('CAMIONETA', false),
                pw.SizedBox(width: 15),
                _buildCheckbox('HERRAMIENTA', false),
                pw.SizedBox(width: 15),
                _buildCheckbox('INFRAESTRUCTURA', false),
                pw.SizedBox(width: 15),
                _buildCheckbox('EQUIPO', true),
                pw.SizedBox(width: 15),
                _buildCheckbox('OTRA', false),
              ],
            ),
          ),
          // Información específica
          pw.Row(
            children: [
              pw.Expanded(
                flex: 3,
                child: _buildInfoCell('CUAL/NOMBRE:', report.assetName),
              ),
              pw.Expanded(child: _buildInfoCell('HOROMETRO:', '')),
              pw.Expanded(child: _buildInfoCell('KILOMETRAJE:', '')),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildUrgencyLevel(String priority) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Row(
        children: [
          pw.Container(
            width: 100,
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'NIVEL DE URGENCIA',
              style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Expanded(
            child: pw.Row(
              children: [
                _buildCheckbox('B', priority == 'Baja'),
                pw.SizedBox(width: 15),
                _buildCheckbox('M', priority == 'Media'),
                pw.SizedBox(width: 15),
                _buildCheckbox('A', priority == 'Alta'),
                pw.SizedBox(width: 15),
                _buildCheckbox('C', priority == 'Crítica'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFaultsDescription(List<String> descriptions) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey300,
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
            ),
            child: pw.Text(
              'DESCRIPCIÓN DE LA SOLICITUD',
              style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Container(
            height: 200,
            padding: const pw.EdgeInsets.all(8),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                for (int i = 0; i < descriptions.length && i < 10; i++) ...[
                  pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Container(
                        width: 20,
                        child: pw.Text(
                          '${i + 1}.',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Text(
                          descriptions[i],
                          style: const pw.TextStyle(fontSize: 10),
                        ),
                      ),
                      // Columnas adicionales
                      pw.Container(
                        width: 60,
                        height: 20,
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(
                            color: PdfColors.black,
                            width: 0.5,
                          ),
                        ),
                        child: pw.Center(
                          child: pw.Text(
                            'SS',
                            style: const pw.TextStyle(fontSize: 8),
                          ),
                        ),
                      ),
                      pw.SizedBox(width: 5),
                      pw.Container(
                        width: 40,
                        height: 20,
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(
                            color: PdfColors.black,
                            width: 0.5,
                          ),
                        ),
                        child: pw.Center(
                          child: pw.Text(
                            'No',
                            style: const pw.TextStyle(fontSize: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 5),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildAffectedSystems() {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey300,
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
            ),
            child: pw.Text(
              'SISTEMA DONDE SE PRESENTA LA SOLICITUD DE TRABAJO',
              style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Column(
              children: [
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: pw.Text(
                        'CA. Cabina y accesorios',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Text(
                        'SLK. Slickline/Wireline',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Text(
                        'SS. Sistema de Suspensión',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Text(
                        'SE. Sistema Eléctrico',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 4),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: pw.Text(
                        'Motor',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Text(
                        'SH. Sistema Hidráulico',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Text(
                        'SD. Sistema de Dirección',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Text(
                        'SF. Sistema de Frenos',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 4),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: pw.Text(
                        'Torre',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Text(
                        'TP. Transmisión y Potencia',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Text(
                        'SM. Sistema de Medición',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Text(
                        'OTRO:_______',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildObservations() {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey300,
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
            ),
            child: pw.Text(
              'OBSERVACIONES:',
              style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Container(
            height: 60,
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(''),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSignatures() {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        children: [
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey300,
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
            ),
            child: pw.Text(
              'USO EXCLUSIVO DE MANTENIMIENTO',
              style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Row(
            children: [
              pw.Expanded(
                child: pw.Container(
                  height: 60,
                  padding: const pw.EdgeInsets.all(8),
                  decoration: pw.BoxDecoration(
                    border: pw.Border(
                      right: pw.BorderSide(color: PdfColors.black),
                    ),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'LÍDER DE MANTENIMIENTO',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                      pw.SizedBox(height: 20),
                      pw.Text('FIRMA', style: const pw.TextStyle(fontSize: 8)),
                      pw.SizedBox(height: 10),
                      pw.Text('FECHA', style: const pw.TextStyle(fontSize: 8)),
                    ],
                  ),
                ),
              ),
              pw.Expanded(
                child: pw.Container(
                  height: 60,
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'TÉCNICO ASIGNADO',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                      pw.SizedBox(height: 20),
                      pw.Text('FIRMA', style: const pw.TextStyle(fontSize: 8)),
                      pw.SizedBox(height: 10),
                      pw.Text('FECHA', style: const pw.TextStyle(fontSize: 8)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(String reportNumber, String date) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(date, style: const pw.TextStyle(fontSize: 8)),
          pw.Text(reportNumber, style: const pw.TextStyle(fontSize: 8)),
        ],
      ),
    );
  }

  // Métodos auxiliares
  static pw.Widget _buildCheckbox(String label, bool isChecked) {
    return pw.Row(
      mainAxisSize: pw.MainAxisSize.min,
      children: [
        pw.Container(
          width: 12,
          height: 12,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black),
          ),
          child: isChecked
              ? pw.Center(
                  child: pw.Text(
                    'X',
                    style: pw.TextStyle(
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                )
              : null,
        ),
        pw.SizedBox(width: 4),
        pw.Text(label, style: const pw.TextStyle(fontSize: 8)),
      ],
    );
  }

  static pw.Widget _buildInfoCell(String label, String value, {int flex = 1}) {
    return pw.Expanded(
      flex: flex,
      child: pw.Container(
        height: 30,
        padding: const pw.EdgeInsets.all(4),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.black, width: 0.5),
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              label,
              style: pw.TextStyle(fontSize: 8, fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(value, style: const pw.TextStyle(fontSize: 10)),
          ],
        ),
      ),
    );
  }

  // Página 2: Registro fotográfico
  static pw.Page _buildPhotographicPage(
    ReportModel report,
    String reportNumber,
    String date,
  ) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(20),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Encabezado
            _buildHeader(
              'SOLICITUD DE TRABAJO O REPORTE DE FALLA',
              reportNumber,
              date,
            ),
            pw.SizedBox(height: 20),

            // Título de registro fotográfico
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(8),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.black),
              ),
              child: pw.Center(
                child: pw.Text(
                  'REGISTRO FOTOGRÁFICO',
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
            ),
            pw.SizedBox(height: 20),

            // Grid de imágenes (2x5 = 10 espacios)
            pw.Expanded(
              child: pw.Column(
                children: [
                  for (int row = 0; row < 5; row++) ...[
                    pw.Expanded(
                      child: pw.Row(
                        children: [
                          _buildImagePlaceholder(
                            'Imagen 1 Descripción ${row * 2 + 1}',
                          ),
                          pw.SizedBox(width: 20),
                          _buildImagePlaceholder(
                            'Imagen 2 Descripción ${row * 2 + 2}',
                          ),
                        ],
                      ),
                    ),
                    if (row < 4) pw.SizedBox(height: 20),
                  ],
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // Página 3: Registro fotográfico adicional
  static pw.Page _buildAdditionalPhotographicPage(
    ReportModel report,
    String reportNumber,
    String date,
  ) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(20),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Encabezado
            _buildHeader(
              'SOLICITUD DE TRABAJO O REPORTE DE FALLA',
              reportNumber,
              date,
            ),
            pw.SizedBox(height: 20),

            // Título de registro fotográfico
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(8),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.black),
              ),
              child: pw.Center(
                child: pw.Text(
                  'REGISTRO FOTOGRÁFICO',
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
            ),
            pw.SizedBox(height: 20),

            // Grid de imágenes adicionales (2x5 = 10 espacios más)
            pw.Expanded(
              child: pw.Column(
                children: [
                  for (int row = 0; row < 5; row++) ...[
                    pw.Expanded(
                      child: pw.Row(
                        children: [
                          _buildImagePlaceholder(
                            'Imagen 1 Descripción ${row * 2 + 6}',
                          ),
                          pw.SizedBox(width: 20),
                          _buildImagePlaceholder(
                            'Imagen 2 Descripción ${row * 2 + 7}',
                          ),
                        ],
                      ),
                    ),
                    if (row < 4) pw.SizedBox(height: 20),
                  ],
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  static pw.Widget _buildImagePlaceholder(String label) {
    return pw.Expanded(
      child: pw.Container(
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.black),
        ),
        child: pw.Column(
          children: [
            pw.Expanded(
              child: pw.Center(
                child: pw.Text(
                  'C',
                  style: pw.TextStyle(
                    fontSize: 48,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.grey,
                  ),
                ),
              ),
            ),
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(4),
              decoration: pw.BoxDecoration(
                border: pw.Border(top: pw.BorderSide(color: PdfColors.black)),
              ),
              child: pw.Center(
                child: pw.Text(
                  label,
                  style: const pw.TextStyle(fontSize: 8),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
