import 'package:flutter/material.dart';

class ResponsiveUtils {
  // Breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  // Métodos para detectar el tipo de dispositivo
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  // Método para obtener el número de columnas según el dispositivo
  static int getGridColumns(BuildContext context) {
    if (isMobile(context)) return 1;
    if (isTablet(context)) return 2;
    return 3;
  }

  // Método para obtener el padding según el dispositivo
  static EdgeInsets getScreenPadding(BuildContext context) {
    if (isMobile(context)) return const EdgeInsets.all(16);
    if (isTablet(context)) return const EdgeInsets.all(24);
    return const EdgeInsets.all(32);
  }

  // Método para obtener el ancho máximo del contenido
  static double getMaxContentWidth(BuildContext context) {
    if (isMobile(context)) return double.infinity;
    if (isTablet(context)) return 800;
    return 1200;
  }

  // Método para obtener el tamaño de fuente según el dispositivo
  static double getFontSize(BuildContext context, double baseFontSize) {
    if (isMobile(context)) return baseFontSize;
    if (isTablet(context)) return baseFontSize * 1.1;
    return baseFontSize * 1.2;
  }

  // Método para obtener el espaciado según el dispositivo
  static double getSpacing(BuildContext context, double baseSpacing) {
    if (isMobile(context)) return baseSpacing;
    if (isTablet(context)) return baseSpacing * 1.2;
    return baseSpacing * 1.5;
  }

  // Método para obtener el tamaño de iconos según el dispositivo
  static double getIconSize(BuildContext context, double baseIconSize) {
    if (isMobile(context)) return baseIconSize;
    if (isTablet(context)) return baseIconSize * 1.1;
    return baseIconSize * 1.2;
  }

  // Método para obtener el radio de borde según el dispositivo
  static double getBorderRadius(BuildContext context, double baseBorderRadius) {
    if (isMobile(context)) return baseBorderRadius;
    if (isTablet(context)) return baseBorderRadius * 1.2;
    return baseBorderRadius * 1.5;
  }

  // Método para obtener la altura de botones según el dispositivo
  static double getButtonHeight(BuildContext context) {
    if (isMobile(context)) return 48;
    if (isTablet(context)) return 52;
    return 56;
  }

  // Método para obtener el ancho de la barra lateral
  static double getSidebarWidth(BuildContext context) {
    if (isMobile(context)) return 280;
    if (isTablet(context)) return 320;
    return 360;
  }

  // Método para determinar si se debe mostrar la navegación lateral
  static bool shouldShowSidebar(BuildContext context) {
    return isDesktop(context);
  }

  // Método para obtener el número de elementos por fila en listas
  static int getListItemsPerRow(BuildContext context) {
    if (isMobile(context)) return 1;
    if (isTablet(context)) return 2;
    return 3;
  }

  // Método para obtener el aspect ratio de las tarjetas
  static double getCardAspectRatio(BuildContext context) {
    if (isMobile(context)) return 1.5;
    if (isTablet(context)) return 1.3;
    return 1.2;
  }

  // Método para obtener el tamaño de imagen según el dispositivo
  static double getImageSize(BuildContext context, double baseImageSize) {
    if (isMobile(context)) return baseImageSize;
    if (isTablet(context)) return baseImageSize * 1.2;
    return baseImageSize * 1.5;
  }

  // Método para obtener el ancho de los modales/diálogos
  static double getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (isMobile(context)) return screenWidth * 0.9;
    if (isTablet(context)) return 500;
    return 600;
  }

  // Método para obtener la orientación preferida
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  // Método para obtener el número de columnas en formularios
  static int getFormColumns(BuildContext context) {
    if (isMobile(context)) return 1;
    if (isTablet(context) && isLandscape(context)) return 2;
    if (isDesktop(context)) return 2;
    return 1;
  }

  // Método para obtener el espaciado entre elementos de formulario
  static double getFormSpacing(BuildContext context) {
    if (isMobile(context)) return 16;
    if (isTablet(context)) return 20;
    return 24;
  }

  // Método para obtener el tamaño de las tarjetas de dashboard
  static double getDashboardCardHeight(BuildContext context) {
    if (isMobile(context)) return 120;
    if (isTablet(context)) return 140;
    return 160;
  }

  // Método para obtener el número de columnas en el dashboard
  static int getDashboardColumns(BuildContext context) {
    if (isMobile(context)) return 2;
    if (isTablet(context)) return 3;
    return 4;
  }

  // Método para obtener el espaciado del dashboard
  static double getDashboardSpacing(BuildContext context) {
    if (isMobile(context)) return 12;
    if (isTablet(context)) return 16;
    return 20;
  }

  // Método para obtener el tamaño de los avatares
  static double getAvatarSize(BuildContext context, double baseAvatarSize) {
    if (isMobile(context)) return baseAvatarSize;
    if (isTablet(context)) return baseAvatarSize * 1.1;
    return baseAvatarSize * 1.2;
  }

  // Método para obtener el ancho de las columnas de tabla
  static double getTableColumnWidth(BuildContext context, double baseWidth) {
    if (isMobile(context)) return baseWidth * 0.8;
    if (isTablet(context)) return baseWidth;
    return baseWidth * 1.2;
  }

  // Método para determinar si se debe usar scroll horizontal en tablas
  static bool shouldUseHorizontalScroll(BuildContext context) {
    return isMobile(context);
  }

  // Método para obtener el número máximo de líneas en texto
  static int getMaxLines(BuildContext context, int baseMaxLines) {
    if (isMobile(context)) return baseMaxLines;
    if (isTablet(context)) return baseMaxLines + 1;
    return baseMaxLines + 2;
  }

  // Método para obtener el ancho de los chips/badges
  static double getChipWidth(BuildContext context) {
    if (isMobile(context)) return 80;
    if (isTablet(context)) return 90;
    return 100;
  }

  // Método para obtener el espaciado en listas
  static double getListSpacing(BuildContext context) {
    if (isMobile(context)) return 8;
    if (isTablet(context)) return 12;
    return 16;
  }

  // Método para obtener el tamaño de los indicadores de carga
  static double getLoadingIndicatorSize(BuildContext context) {
    if (isMobile(context)) return 24;
    if (isTablet(context)) return 28;
    return 32;
  }

  // Método para obtener el ancho de los campos de búsqueda
  static double getSearchFieldWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (isMobile(context)) return screenWidth * 0.8;
    if (isTablet(context)) return 400;
    return 500;
  }

  // Método para obtener el número de elementos visibles en carruseles
  static int getCarouselVisibleItems(BuildContext context) {
    if (isMobile(context)) return 1;
    if (isTablet(context)) return 2;
    return 3;
  }

  // Método para obtener el espaciado en grids
  static double getGridSpacing(BuildContext context) {
    if (isMobile(context)) return 8;
    if (isTablet(context)) return 12;
    return 16;
  }

  // Método para obtener el tamaño de los FABs
  static double getFabSize(BuildContext context) {
    if (isMobile(context)) return 56;
    if (isTablet(context)) return 60;
    return 64;
  }
}
