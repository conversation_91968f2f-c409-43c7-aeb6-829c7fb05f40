import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/app_utils.dart';
import '../../../data/models/asset_model.dart';
import '../../providers/asset_provider.dart';
import 'asset_form_screen.dart';

class AssetDetailScreen extends StatelessWidget {
  final AssetModel asset;

  const AssetDetailScreen({super.key, required this.asset});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(asset.name),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _navigateToEdit(context);
                  break;
                case 'delete':
                  _showDeleteDialog(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: AppColors.primary),
                    SizedBox(width: 8),
                    Text('Editar'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('Eliminar'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header con información principal
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: _getStatusColor(
                              asset.status,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            _getAssetIcon(asset.type),
                            size: 32,
                            color: _getStatusColor(asset.status),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                asset.name,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Código: ${asset.code}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(child: _buildStatusChip(asset.status)),
                        const SizedBox(width: 8),
                        Expanded(child: _buildTypeChip(asset.type)),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Información básica
            _buildInfoSection(
              title: 'Información Básica',
              children: [
                if (asset.description?.isNotEmpty == true)
                  _buildInfoRow('Descripción', asset.description!),
                if (asset.location?.isNotEmpty == true)
                  _buildInfoRow('Ubicación', asset.location!),
                _buildInfoRow(
                  'Fecha de Creación',
                  AppUtils.formatDate(asset.createdAt),
                ),
                _buildInfoRow(
                  'Última Actualización',
                  asset.updatedAt != null
                      ? AppUtils.formatDate(asset.updatedAt!)
                      : 'No disponible',
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Información técnica
            if (_hasTechnicalInfo())
              _buildInfoSection(
                title: 'Información Técnica',
                children: [
                  if (asset.serialNumber?.isNotEmpty == true)
                    _buildInfoRow('Número de Serie', asset.serialNumber!),
                  if (asset.model?.isNotEmpty == true)
                    _buildInfoRow('Modelo', asset.model!),
                  if (asset.brand?.isNotEmpty == true)
                    _buildInfoRow('Marca', asset.brand!),
                ],
              ),

            if (_hasTechnicalInfo()) const SizedBox(height: 16),

            // Información financiera
            if (_hasFinancialInfo())
              _buildInfoSection(
                title: 'Información Financiera',
                children: [
                  if (asset.purchaseDate != null)
                    _buildInfoRow(
                      'Fecha de Compra',
                      AppUtils.formatDate(asset.purchaseDate!),
                    ),
                  if (asset.purchasePrice != null)
                    _buildInfoRow(
                      'Precio de Compra',
                      '\$${asset.purchasePrice!.toStringAsFixed(2)}',
                    ),
                ],
              ),

            const SizedBox(height: 24),

            // Acciones rápidas
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Acciones Rápidas',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildActionButton(
                            icon: Icons.report_problem,
                            label: 'Reportar Falla',
                            color: AppColors.warning,
                            onTap: () {
                              // TODO: Implementar reporte de falla
                              AppUtils.showSnackBar(
                                context,
                                'Reportar falla próximamente',
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildActionButton(
                            icon: Icons.build,
                            label: 'Mantenimiento',
                            color: AppColors.info,
                            onTap: () {
                              // TODO: Implementar orden de mantenimiento
                              AppUtils.showSnackBar(
                                context,
                                'Mantenimiento próximamente',
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getStatusColor(status),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.circle, size: 8, color: Colors.white),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              status,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeChip(String type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.info),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getAssetIcon(type), size: 14, color: AppColors.info),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              type,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.info,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  bool _hasTechnicalInfo() {
    return asset.serialNumber?.isNotEmpty == true ||
        asset.model?.isNotEmpty == true ||
        asset.brand?.isNotEmpty == true;
  }

  bool _hasFinancialInfo() {
    return asset.purchaseDate != null || asset.purchasePrice != null;
  }

  IconData _getAssetIcon(String type) {
    switch (type.toLowerCase()) {
      case 'maquinaria':
        return Icons.precision_manufacturing;
      case 'vehículo':
        return Icons.directions_car;
      case 'equipo':
        return Icons.build_circle;
      case 'herramienta':
        return Icons.build;
      case 'mobiliario':
        return Icons.chair;
      default:
        return Icons.inventory_2;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'operativo':
        return AppColors.success;
      case 'mantenimiento':
        return AppColors.warning;
      case 'fuera de servicio':
        return AppColors.error;
      case 'en reparación':
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }

  void _navigateToEdit(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => AssetFormScreen(asset: asset)),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Activo'),
        content: Text('¿Estás seguro de que deseas eliminar "${asset.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => _deleteAsset(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );
  }

  void _deleteAsset(BuildContext context) async {
    Navigator.of(context).pop(); // Cerrar diálogo

    final assetProvider = Provider.of<AssetProvider>(context, listen: false);
    final success = await assetProvider.deleteAsset(asset.id);

    if (context.mounted) {
      if (success) {
        AppUtils.showSuccessSnackBar(context, 'Activo eliminado exitosamente');
        Navigator.of(context).pop(); // Volver a la lista
      } else {
        AppUtils.showErrorSnackBar(
          context,
          assetProvider.errorMessage ?? 'Error al eliminar el activo',
        );
      }
    }
  }
}
