import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/app_utils.dart';
import '../../../data/models/asset_model.dart';
import '../../../data/models/report_model.dart';
import '../../providers/asset_provider.dart';
import '../../providers/report_provider.dart';
import '../reports/report_detail_screen.dart';
import '../reports/report_form_screen.dart';
import 'asset_form_screen.dart';

class AssetDetailScreen extends StatefulWidget {
  final AssetModel asset;

  const AssetDetailScreen({super.key, required this.asset});

  @override
  State<AssetDetailScreen> createState() => _AssetDetailScreenState();
}

class _AssetDetailScreenState extends State<AssetDetailScreen>
    with SingleTickerProviderStateMixin {
  late AssetModel _currentAsset;
  late TabController _tabController;
  List<ReportModel> _assetReports = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentAsset = widget.asset;
    _tabController = TabController(length: 4, vsync: this);
    _loadAssetReports();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAssetReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportProvider = Provider.of<ReportProvider>(
        context,
        listen: false,
      );
      final allReports = reportProvider.reports;

      // Filtrar reportes por activo
      _assetReports = allReports
          .where((report) => report.assetId == _currentAsset.id)
          .toList();

      // Ordenar por fecha más reciente
      _assetReports.sort((a, b) => b.reportedAt.compareTo(a.reportedAt));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al cargar reportes: ${e.toString()}')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshAsset() async {
    await _loadAssetReports();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(_currentAsset.name),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.edit), onPressed: _editAsset),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'qr',
                child: Row(
                  children: [
                    Icon(Icons.qr_code, size: 20),
                    SizedBox(width: 8),
                    Text('Código QR'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download, size: 20),
                    SizedBox(width: 8),
                    Text('Exportar Hoja de Vida'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('Eliminar', style: TextStyle(color: AppColors.error)),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.info), text: 'General'),
            Tab(icon: Icon(Icons.report), text: 'Reportes'),
            Tab(icon: Icon(Icons.build), text: 'Mantenimiento'),
            Tab(icon: Icon(Icons.timeline), text: 'Historial'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildGeneralTab(),
          _buildReportsTab(),
          _buildMaintenanceTab(),
          _buildHistoryTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createReport,
        backgroundColor: AppColors.error,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.report_problem),
        label: const Text('Reportar Falla'),
      ),
    );
  }

  // Métodos para construir las pestañas
  Widget _buildGeneralTab() {
    return RefreshIndicator(
      onRefresh: _refreshAsset,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header del activo
            _buildAssetHeader(),
            const SizedBox(height: 24),

            // Información básica
            _buildInfoSection(
              title: 'Información Básica',
              children: [
                if (_currentAsset.description?.isNotEmpty == true)
                  _buildInfoRow('Descripción', _currentAsset.description!),
                if (_currentAsset.location?.isNotEmpty == true)
                  _buildInfoRow('Ubicación', _currentAsset.location!),
                _buildInfoRow(
                  'Fecha de Creación',
                  AppUtils.formatDate(_currentAsset.createdAt),
                ),
                _buildInfoRow(
                  'Última Actualización',
                  _currentAsset.updatedAt != null
                      ? AppUtils.formatDate(_currentAsset.updatedAt!)
                      : 'No disponible',
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Información técnica
            if (_hasTechnicalInfo())
              _buildInfoSection(
                title: 'Información Técnica',
                children: [
                  if (_currentAsset.serialNumber?.isNotEmpty == true)
                    _buildInfoRow(
                      'Número de Serie',
                      _currentAsset.serialNumber!,
                    ),
                  if (_currentAsset.model?.isNotEmpty == true)
                    _buildInfoRow('Modelo', _currentAsset.model!),
                  if (_currentAsset.brand?.isNotEmpty == true)
                    _buildInfoRow('Marca', _currentAsset.brand!),
                ],
              ),

            if (_hasTechnicalInfo()) const SizedBox(height: 16),

            // Información financiera
            if (_hasFinancialInfo())
              _buildInfoSection(
                title: 'Información Financiera',
                children: [
                  if (_currentAsset.purchaseDate != null)
                    _buildInfoRow(
                      'Fecha de Compra',
                      AppUtils.formatDate(_currentAsset.purchaseDate!),
                    ),
                  if (_currentAsset.purchasePrice != null)
                    _buildInfoRow(
                      'Precio de Compra',
                      '\$${_currentAsset.purchasePrice!.toStringAsFixed(2)}',
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsTab() {
    return RefreshIndicator(
      onRefresh: _loadAssetReports,
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _assetReports.isEmpty
          ? _buildEmptyReports()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _assetReports.length,
              itemBuilder: (context, index) {
                return _buildReportCard(_assetReports[index]);
              },
            ),
    );
  }

  Widget _buildMaintenanceTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.build, size: 64, color: AppColors.textSecondary),
          SizedBox(height: 16),
          Text(
            'Historial de Mantenimiento',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Próximamente disponible',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.timeline, size: 64, color: AppColors.textSecondary),
          SizedBox(height: 16),
          Text(
            'Historial Completo',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Próximamente disponible',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildAssetHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Icono del activo
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _getAssetTypeColor(
                    _currentAsset.type,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getAssetIcon(_currentAsset.type),
                  size: 32,
                  color: _getAssetTypeColor(_currentAsset.type),
                ),
              ),
              const SizedBox(width: 16),

              // Información principal
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentAsset.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _currentAsset.type,
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Estado del activo
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(_currentAsset.status),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        _currentAsset.status,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // ID y código
          Row(
            children: [
              Expanded(
                child: _buildInfoChip('ID', _currentAsset.id, Icons.tag),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoChip(
                  'Código',
                  _currentAsset.code,
                  Icons.qr_code,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Icon(icon, size: 16, color: AppColors.textSecondary),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 10,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getStatusColor(status),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.circle, size: 8, color: Colors.white),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              status,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeChip(String type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.info),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getAssetIcon(type), size: 14, color: AppColors.info),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              type,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.info,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Métodos para reportes
  Widget _buildEmptyReports() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.report_outlined, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'Sin reportes',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Este activo no tiene reportes registrados',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(ReportModel report) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToReportDetail(report),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Icono de prioridad
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getReportPriorityColor(report.priority),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getReportPriorityIcon(report.priority),
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Información del reporte
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          report.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          AppUtils.formatDate(report.reportedAt),
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Estado del reporte
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getReportStatusColor(
                        report.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _getReportStatusColor(report.status),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      report.status,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: _getReportStatusColor(report.status),
                      ),
                    ),
                  ),
                ],
              ),

              if (report.description.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  report.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Métodos de utilidad
  bool _hasTechnicalInfo() {
    return _currentAsset.serialNumber?.isNotEmpty == true ||
        _currentAsset.model?.isNotEmpty == true ||
        _currentAsset.brand?.isNotEmpty == true;
  }

  bool _hasFinancialInfo() {
    return _currentAsset.purchaseDate != null ||
        _currentAsset.purchasePrice != null;
  }

  Color _getAssetTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'maquinaria':
        return Colors.orange;
      case 'vehículo':
        return Colors.blue;
      case 'equipo':
        return Colors.green;
      case 'herramienta':
        return Colors.purple;
      case 'mobiliario':
        return Colors.brown;
      default:
        return AppColors.primary;
    }
  }

  Color _getReportPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Colors.black;
      case 'alta':
        return Colors.red;
      case 'media':
        return Colors.blue;
      case 'baja':
        return Colors.amber;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getReportPriorityIcon(String priority) {
    switch (priority.toLowerCase()) {
      case 'crítica':
        return Icons.emergency;
      case 'alta':
        return Icons.dangerous;
      case 'media':
        return Icons.warning;
      case 'baja':
        return Icons.info;
      default:
        return Icons.priority_high;
    }
  }

  Color _getReportStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'abierto':
        return AppColors.info;
      case 'en progreso':
        return AppColors.warning;
      case 'resuelto':
        return AppColors.success;
      case 'cerrado':
        return Colors.green.shade900;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getAssetIcon(String type) {
    switch (type.toLowerCase()) {
      case 'maquinaria':
        return Icons.precision_manufacturing;
      case 'vehículo':
        return Icons.directions_car;
      case 'equipo':
        return Icons.build_circle;
      case 'herramienta':
        return Icons.build;
      case 'mobiliario':
        return Icons.chair;
      default:
        return Icons.inventory_2;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'operativo':
        return AppColors.success;
      case 'mantenimiento':
        return AppColors.warning;
      case 'fuera de servicio':
        return AppColors.error;
      case 'en reparación':
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }

  // Métodos de navegación y acciones
  void _navigateToReportDetail(ReportModel report) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ReportDetailScreen(report: report),
      ),
    );
  }

  void _createReport() {
    Navigator.of(context)
        .push(MaterialPageRoute(builder: (context) => const ReportFormScreen()))
        .then((_) {
          // Recargar reportes cuando regrese del formulario
          _loadAssetReports();
        });
  }

  void _editAsset() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => AssetFormScreen(asset: _currentAsset),
          ),
        )
        .then((_) {
          // Refrescar cuando regrese del formulario
          _refreshAsset();
        });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'qr':
        _showQRCode();
        break;
      case 'export':
        _exportAssetData();
        break;
      case 'delete':
        _showDeleteDialog();
        break;
    }
  }

  void _showQRCode() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Código QR'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.border),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  const Icon(Icons.qr_code, size: 100),
                  const SizedBox(height: 8),
                  Text(
                    'ID: ${_currentAsset.id}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Funcionalidad de QR próximamente',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  void _exportAssetData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exportar hoja de vida próximamente'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Activo'),
        content: Text(
          '¿Estás seguro de que deseas eliminar "${_currentAsset.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => _deleteAsset(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );
  }

  void _deleteAsset() async {
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    navigator.pop(); // Cerrar diálogo

    final assetProvider = Provider.of<AssetProvider>(context, listen: false);
    final success = await assetProvider.deleteAsset(_currentAsset.id);

    if (mounted) {
      if (success) {
        AppUtils.showSuccessSnackBar(context, 'Activo eliminado exitosamente');
        navigator.pop(); // Volver a la lista
      } else {
        AppUtils.showErrorSnackBar(
          context,
          assetProvider.errorMessage ?? 'Error al eliminar el activo',
        );
      }
    }
  }
}
