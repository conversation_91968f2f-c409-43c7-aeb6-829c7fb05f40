# 🚀 Configuración Paso a Paso de Appwrite para MTTO 60

## Paso 1: Crear Proyecto en Appwrite Cloud

### 1.1 Registro y Proyecto
1. Ve a [cloud.appwrite.io](https://cloud.appwrite.io)
2. Crea cuenta o inicia sesión
3. <PERSON>lic en "Create Project"
4. Nombre: `MTTO 60`
5. Descripción: `Sistema de Mantenimiento de Activos`

### 1.2 Configurar Plataformas
**Para Flutter (Móvil):**
1. Ve a "Settings" → "Platforms"
2. Clic "Add Platform" → "Flutter"
3. Nombre: `MTTO 60 Mobile`
4. Package Name: `com.example.mtto_60`

**Para Web:**
1. Clic "Add Platform" → "Web"
2. Nombre: `MTTO 60 Web`
3. Hostname: `localhost` (desarrollo)
4. También agrega tu dominio de producción

### 1.3 Obtener Credenciales
1. Ve a "Settings" → "View API Keys"
2. Copia el **Project ID**
3. El endpoint será: `https://cloud.appwrite.io/v1`

## Paso 2: Actualizar Credenciales en Flutter

Reemplaza en `lib/core/constants/app_constants.dart`:

```dart
static const String appwriteProjectId = 'TU_PROJECT_ID_REAL_AQUI';
```

## Paso 3: Crear Base de Datos

### 3.1 Crear Database
1. Ve a "Databases" en tu proyecto
2. Clic "Create Database"
3. Database ID: `mtto_60_db`
4. Name: `MTTO 60 Database`

### 3.2 Actualizar Database ID
En `app_constants.dart`:
```dart
static const String appwriteDatabaseId = 'mtto_60_db';
```

## Paso 4: Crear Colecciones

### 4.1 Colección: users
1. En tu database, clic "Create Collection"
2. Collection ID: `users`
3. Name: `Users`

**Atributos:**
- `email` → String, Size: 255, Required: ✅
- `name` → String, Size: 100, Required: ❌
- `phone` → String, Size: 20, Required: ❌
- `role` → String, Size: 50, Required: ✅, Default: "Reportador"
- `avatar_url` → String, Size: 500, Required: ❌
- `created_at` → DateTime, Required: ✅
- `updated_at` → DateTime, Required: ❌
- `is_active` → Boolean, Required: ✅, Default: true

**Permisos:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `admins`

### 4.2 Colección: assets
1. Clic "Create Collection"
2. Collection ID: `assets`
3. Name: `Assets`

**Atributos:**
- `name` → String, Size: 200, Required: ✅
- `code` → String, Size: 50, Required: ✅, Unique: ✅
- `type` → String, Size: 50, Required: ✅
- `description` → String, Size: 1000, Required: ❌
- `location` → String, Size: 200, Required: ❌
- `status` → String, Size: 50, Required: ✅, Default: "Activo"
- `brand` → String, Size: 100, Required: ❌
- `model` → String, Size: 100, Required: ❌
- `serial_number` → String, Size: 100, Required: ❌
- `purchase_date` → DateTime, Required: ❌
- `purchase_price` → Float, Required: ❌
- `image_url` → String, Size: 500, Required: ❌
- `qr_code` → String, Size: 200, Required: ❌
- `custom_fields` → String, Size: 2000, Required: ❌
- `created_at` → DateTime, Required: ✅
- `updated_at` → DateTime, Required: ❌
- `created_by` → String, Size: 50, Required: ✅

**Permisos:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `admins`

### 4.3 Colección: reports
1. Clic "Create Collection"
2. Collection ID: `reports`
3. Name: `Reports`

**Atributos:**
- `asset_id` → String, Size: 50, Required: ✅
- `title` → String, Size: 200, Required: ✅
- `description` → String, Size: 2000, Required: ✅
- `priority` → String, Size: 50, Required: ✅, Default: "Media"
- `status` → String, Size: 50, Required: ✅, Default: "Pendiente"
- `image_urls` → String, Size: 2000, Required: ❌
- `location` → String, Size: 200, Required: ❌
- `reported_at` → DateTime, Required: ✅
- `reported_by` → String, Size: 50, Required: ✅
- `assigned_to` → String, Size: 50, Required: ❌
- `assigned_at` → DateTime, Required: ❌
- `resolved_at` → DateTime, Required: ❌
- `resolution` → String, Size: 2000, Required: ❌
- `metadata` → String, Size: 2000, Required: ❌

**Permisos:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `admins`

### 4.4 Colección: work_orders
1. Clic "Create Collection"
2. Collection ID: `work_orders`
3. Name: `Work Orders`

**Atributos:**
- `report_id` → String, Size: 50, Required: ❌
- `asset_id` → String, Size: 50, Required: ✅
- `title` → String, Size: 200, Required: ✅
- `description` → String, Size: 2000, Required: ✅
- `type` → String, Size: 50, Required: ✅
- `priority` → String, Size: 50, Required: ✅, Default: "Media"
- `status` → String, Size: 50, Required: ✅, Default: "Pendiente"
- `assigned_to` → String, Size: 50, Required: ❌
- `scheduled_date` → DateTime, Required: ❌
- `started_at` → DateTime, Required: ❌
- `completed_at` → DateTime, Required: ❌
- `estimated_hours` → Integer, Required: ❌
- `actual_hours` → Integer, Required: ❌
- `materials` → String, Size: 2000, Required: ❌
- `tasks` → String, Size: 2000, Required: ❌
- `notes` → String, Size: 2000, Required: ❌
- `image_urls` → String, Size: 2000, Required: ❌
- `signature_url` → String, Size: 500, Required: ❌
- `created_at` → DateTime, Required: ✅
- `created_by` → String, Size: 50, Required: ✅
- `updated_at` → DateTime, Required: ❌

**Permisos:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `admins`

## Paso 5: Configurar Storage

### 5.1 Bucket: asset-images
1. Ve a "Storage"
2. Clic "Create Bucket"
3. Bucket ID: `asset-images`
4. Name: `Asset Images`
5. File Security: ✅ Enabled
6. Max File Size: 10MB
7. Allowed Extensions: `jpg,jpeg,png,webp`

**Permisos:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `admins`

### 5.2 Bucket: documents
1. Clic "Create Bucket"
2. Bucket ID: `documents`
3. Name: `Documents`
4. File Security: ✅ Enabled
5. Max File Size: 50MB
6. Allowed Extensions: `pdf,doc,docx,xls,xlsx`

**Permisos:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `admins`

## Paso 6: Configurar Autenticación

1. Ve a "Auth" → "Settings"
2. Habilita "Email/Password"
3. Configura URLs de redirección si necesario
4. Opcionalmente habilita otros proveedores (Google, etc.)

## Paso 7: Probar Configuración

```bash
# Instalar dependencias
flutter pub get

# Ejecutar análisis
flutter analyze

# Probar en web
flutter run -d chrome

# Probar en móvil
flutter run
```

## ✅ Checklist de Configuración

- [ ] Proyecto creado en Appwrite Cloud
- [ ] Plataformas Flutter y Web agregadas
- [ ] Credenciales copiadas y actualizadas en Flutter
- [ ] Base de datos `mtto_60_db` creada
- [ ] Colección `users` creada con atributos y permisos
- [ ] Colección `assets` creada con atributos y permisos
- [ ] Colección `reports` creada con atributos y permisos
- [ ] Colección `work_orders` creada con atributos y permisos
- [ ] Bucket `asset-images` creado con permisos
- [ ] Bucket `documents` creado con permisos
- [ ] Autenticación Email/Password habilitada
- [ ] Aplicación probada en web y móvil

## 🚨 Troubleshooting

**Error de conexión:**
- Verifica Project ID y endpoint
- Asegúrate que las plataformas estén configuradas
- Revisa la consola del navegador para errores CORS

**Errores de permisos:**
- Verifica que los permisos de colecciones estén correctos
- Asegúrate que el usuario esté autenticado
- Revisa los roles en la consola de Appwrite

¡Una vez completado este checklist, tu aplicación MTTO 60 estará lista para funcionar con Appwrite! 🎉
