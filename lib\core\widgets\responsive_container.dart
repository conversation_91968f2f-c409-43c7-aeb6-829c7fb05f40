import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? maxWidth;
  final bool centerContent;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.maxWidth,
    this.centerContent = true,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = padding ?? ResponsiveUtils.getScreenPadding(context);
    final responsiveMaxWidth = maxWidth ?? ResponsiveUtils.getMaxContentWidth(context);

    Widget content = Container(
      width: double.infinity,
      constraints: BoxConstraints(maxWidth: responsiveMaxWidth),
      padding: responsivePadding,
      child: child,
    );

    if (centerContent && ResponsiveUtils.isDesktop(context)) {
      content = Center(child: content);
    }

    return content;
  }
}

class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? columns;
  final double? spacing;
  final double? runSpacing;
  final double? childAspectRatio;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.columns,
    this.spacing,
    this.runSpacing,
    this.childAspectRatio,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveColumns = columns ?? ResponsiveUtils.getGridColumns(context);
    final responsiveSpacing = spacing ?? ResponsiveUtils.getGridSpacing(context);
    final responsiveRunSpacing = runSpacing ?? ResponsiveUtils.getGridSpacing(context);
    final responsiveAspectRatio = childAspectRatio ?? ResponsiveUtils.getCardAspectRatio(context);

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: responsiveColumns,
        crossAxisSpacing: responsiveSpacing,
        mainAxisSpacing: responsiveRunSpacing,
        childAspectRatio: responsiveAspectRatio,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

class ResponsiveRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final bool wrapOnMobile;

  const ResponsiveRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.wrapOnMobile = true,
  });

  @override
  Widget build(BuildContext context) {
    if (wrapOnMobile && ResponsiveUtils.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children.map((child) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: child,
        )).toList(),
      );
    }

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children,
    );
  }
}

class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? elevation;
  final Color? color;
  final BorderRadius? borderRadius;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.elevation,
    this.color,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = padding ?? EdgeInsets.all(
      ResponsiveUtils.getSpacing(context, 16),
    );
    final responsiveElevation = elevation ?? (ResponsiveUtils.isMobile(context) ? 2 : 4);
    final responsiveBorderRadius = borderRadius ?? BorderRadius.circular(
      ResponsiveUtils.getBorderRadius(context, 12),
    );

    return Card(
      elevation: responsiveElevation,
      color: color,
      shape: RoundedRectangleBorder(borderRadius: responsiveBorderRadius),
      child: Padding(
        padding: responsivePadding,
        child: child,
      ),
    );
  }
}

class ResponsiveButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final bool isElevated;

  const ResponsiveButton({
    super.key,
    required this.child,
    this.onPressed,
    this.style,
    this.isElevated = true,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveHeight = ResponsiveUtils.getButtonHeight(context);
    final responsiveBorderRadius = ResponsiveUtils.getBorderRadius(context, 8);

    final defaultStyle = isElevated
        ? ElevatedButton.styleFrom(
            minimumSize: Size(double.infinity, responsiveHeight),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(responsiveBorderRadius),
            ),
          )
        : OutlinedButton.styleFrom(
            minimumSize: Size(double.infinity, responsiveHeight),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(responsiveBorderRadius),
            ),
          );

    final mergedStyle = style != null ? defaultStyle.merge(style) : defaultStyle;

    if (isElevated) {
      return ElevatedButton(
        onPressed: onPressed,
        style: mergedStyle,
        child: child,
      );
    } else {
      return OutlinedButton(
        onPressed: onPressed,
        style: mergedStyle,
        child: child,
      );
    }
  }
}

class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double fontSizeMultiplier;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.fontSizeMultiplier = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    final baseStyle = style ?? Theme.of(context).textTheme.bodyMedium!;
    final baseFontSize = baseStyle.fontSize ?? 14;
    final responsiveFontSize = ResponsiveUtils.getFontSize(
      context,
      baseFontSize * fontSizeMultiplier,
    );

    final responsiveMaxLines = maxLines != null
        ? ResponsiveUtils.getMaxLines(context, maxLines!)
        : null;

    return Text(
      text,
      style: baseStyle.copyWith(fontSize: responsiveFontSize),
      textAlign: textAlign,
      maxLines: responsiveMaxLines,
      overflow: overflow,
    );
  }
}

class ResponsiveIcon extends StatelessWidget {
  final IconData icon;
  final double? size;
  final Color? color;
  final double sizeMultiplier;

  const ResponsiveIcon(
    this.icon, {
    super.key,
    this.size,
    this.color,
    this.sizeMultiplier = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    final baseSize = size ?? 24;
    final responsiveSize = ResponsiveUtils.getIconSize(
      context,
      baseSize * sizeMultiplier,
    );

    return Icon(
      icon,
      size: responsiveSize,
      color: color,
    );
  }
}

class ResponsiveSpacing extends StatelessWidget {
  final double height;
  final double? width;

  const ResponsiveSpacing({
    super.key,
    required this.height,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveHeight = ResponsiveUtils.getSpacing(context, height);
    final responsiveWidth = width != null
        ? ResponsiveUtils.getSpacing(context, width!)
        : null;

    return SizedBox(
      height: responsiveHeight,
      width: responsiveWidth,
    );
  }
}

class ResponsiveFormField extends StatelessWidget {
  final Widget child;
  final String? label;
  final bool isRequired;

  const ResponsiveFormField({
    super.key,
    required this.child,
    this.label,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    final spacing = ResponsiveUtils.getFormSpacing(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          ResponsiveText(
            label! + (isRequired ? ' *' : ''),
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: spacing / 2),
        ],
        child,
        SizedBox(height: spacing),
      ],
    );
  }
}

class ResponsiveDialog extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;

  const ResponsiveDialog({
    super.key,
    required this.child,
    this.title,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final dialogWidth = ResponsiveUtils.getDialogWidth(context);

    return Dialog(
      child: Container(
        width: dialogWidth,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null)
              Padding(
                padding: const EdgeInsets.all(24),
                child: ResponsiveText(
                  title!,
                  style: Theme.of(context).textTheme.headlineSmall,
                  fontSizeMultiplier: 1.2,
                ),
              ),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: child,
              ),
            ),
            if (actions != null)
              Padding(
                padding: const EdgeInsets.all(24),
                child: ResponsiveRow(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions!,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
